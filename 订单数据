[{'creatTime': '2025-03-01 14:31:13', 'errorReason': '', 'failType': '', 'orderNumber': 'ZJ20241225736108', 'robotName': '张弘玺', 'rpaCode': '', 'status': '1', 'wfName': '业务解构机器人【自动】'}, {'creatTime': '2025-02-28 19:01:33', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022811305191', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-28 19:01:17', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022811305194', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-28 19:01:01', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511238057', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-28 19:00:45', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511237835', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-28 19:00:30', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511235943', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-28 17:16:55', 'errorReason': '', 'failType': '', 'orderNumber': 'ZJ20241216973229', 'robotName': '张弘玺', 'rpaCode': '', 'status': '1', 'wfName': '业务解构机器人【自动】'}, {'creatTime': '2025-02-28 17:06:15', 'errorReason': '', 'failType': '', 'orderNumber': 'ZJ20240402321657', 'robotName': '张弘玺', 'rpaCode': '', 'status': '1', 'wfName': '业务解构机器人【自动】'}, {'creatTime': '2025-02-28 17:02:16', 'errorReason': '账号或密码错误', 'failType': '3', 'orderNumber': 'b52814c2-f5b2-11ef-95d5-fa163ea56826', 'orderTitle': 'EIP系统登录', 'robotName': '15305809991', 'rpaCode': 'bid_bond_receive_dept_approve', 'status': '0', 'wfName': '投标保证金收款审批（部门领导）'}, {'creatTime': '2025-02-28 16:52:53', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA250200015', 'robotName': '吴超群', 'rpaCode': 'project_initiation_approve', 'status': '1', 'wfName': '项目立项审批'}, {'creatTime': '2025-02-28 16:40:56', 'errorReason': '', 'failType': '', 'orderNumber': 'ZJ20250106667660', 'robotName': '张弘玺', 'rpaCode': '', 'status': '1', 'wfName': '业务解构机器人【自动】'}, {'creatTime': '2025-02-28 16:24:53', 'errorReason': '账号或密码错误', 'failType': '3', 'orderNumber': '7c505cff-f5ad-11ef-a855-fa163ea56826', 'orderTitle': 'EIP系统登录', 'robotName': 'zhoupl', 'rpaCode': 'successful_bidder_pay_dept_approve', 'status': '0', 'wfName': '中标服务费付款审批（部门领导）'}, {'creatTime': '2025-02-28 16:22:27', 'errorReason': '账号或密码错误', 'failType': '3', 'orderNumber': '256187ae-f5ad-11ef-a6af-fa163ea56826', 'orderTitle': 'EIP系统登录', 'robotName': '15305809991', 'rpaCode': 'successful_bidder_pay_dept_approve', 'status': '0', 'wfName': '中标服务费付款审批（部门领导）'}, {'creatTime': '2025-02-28 16:13:22', 'errorReason': '账号或密码错误', 'failType': '3', 'orderNumber': 'e0b1f7fa-f5ab-11ef-b971-fa163ea56826', 'orderTitle': 'EIP系统登录', 'robotName': 'zhoupl', 'rpaCode': 'bid_bond_pay_dept_approve', 'status': '0', 'wfName': '投标保证金付款审批（部门领导）'}, {'creatTime': '2025-02-28 16:12:06', 'errorReason': '账号或密码错误', 'failType': '3', 'orderNumber': 'b321ac31-f5ab-11ef-bbcf-fa163ea56826', 'orderTitle': 'EIP系统登录', 'robotName': '15305809991', 'rpaCode': 'bid_bond_pay_dept_approve', 'status': '0', 'wfName': '投标保证金付款审批（部门领导）'}, {'creatTime': '2025-02-28 16:04:11', 'errorReason': '账号或密码错误', 'failType': '3', 'orderNumber': '981c2390-f5aa-11ef-af19-fa163ea56826', 'orderTitle': 'EIP系统登录', 'robotName': 'zhoupl', 'rpaCode': 'bid_bond_receive_dept_approve', 'status': '0', 'wfName': '投标保证金收款审批（部门领导）'}, {'creatTime': '2025-02-28 16:02:06', 'errorReason': '账号或密码错误', 'failType': '3', 'orderNumber': '4d93be6e-f5aa-11ef-af5b-fa163ea56826', 'orderTitle': 'EIP系统登录', 'robotName': '15305809991', 'rpaCode': 'bid_bond_receive_dept_approve', 'status': '0', 'wfName': '投标保证金收款审批（部门领导）'}, {'creatTime': '2025-02-28 15:24:53', 'errorReason': '账号或密码错误', 'failType': '3', 'orderNumber': '1aabacca-f5a5-11ef-bc5f-fa163ea56826', 'orderTitle': 'EIP系统登录', 'robotName': 'zhoupl', 'rpaCode': 'successful_bidder_pay_dept_approve', 'status': '0', 'wfName': '中标服务费付款审批（部门领导）'}, {'creatTime': '2025-02-28 15:22:27', 'errorReason': '账号或密码错误', 'failType': '3', 'orderNumber': 'c392a194-f5a4-11ef-abd8-fa163ea56826', 'orderTitle': 'EIP系统登录', 'robotName': '15305809991', 'rpaCode': 'successful_bidder_pay_dept_approve', 'status': '0', 'wfName': '中标服务费付款审批（部门领导）'}, {'creatTime': '2025-02-28 15:13:23', 'errorReason': '账号或密码错误', 'failType': '3', 'orderNumber': '7f1b4d2a-f5a3-11ef-8d9e-fa163ea56826', 'orderTitle': 'EIP系统登录', 'robotName': 'zhoupl', 'rpaCode': 'bid_bond_pay_dept_approve', 'status': '0', 'wfName': '投标保证金付款审批（部门领导）'}, {'creatTime': '2025-02-28 15:12:06', 'errorReason': '账号或密码错误', 'failType': '3', 'orderNumber': '5132094b-f5a3-11ef-b9b8-fa163ea56826', 'orderTitle': 'EIP系统登录', 'robotName': '15305809991', 'rpaCode': 'bid_bond_pay_dept_approve', 'status': '0', 'wfName': '投标保证金付款审批（部门领导）'}, {'creatTime': '2025-02-28 15:04:23', 'errorReason': '账号或密码错误', 'failType': '3', 'orderNumber': '3d71a8ee-f5a2-11ef-8209-fa163ea56826', 'orderTitle': 'EIP系统登录', 'robotName': 'zhoupl', 'rpaCode': 'bid_bond_receive_dept_approve', 'status': '0', 'wfName': '投标保证金收款审批（部门领导）'}, {'creatTime': '2025-02-28 15:02:19', 'errorReason': '账号或密码错误', 'failType': '3', 'orderNumber': 'f3343e85-f5a1-11ef-94be-fa163ea56826', 'orderTitle': 'EIP系统登录', 'robotName': '15305809991', 'rpaCode': 'bid_bond_receive_dept_approve', 'status': '0', 'wfName': '投标保证金收款审批（部门领导）'}, {'creatTime': '2025-02-28 14:53:30', 'errorReason': '', 'failType': '', 'orderNumber': '71487699', 'orderTitle': '工作联系单:柳晨驰(文宣行业产品部)发出工作联系单', 'robotName': '', 'rpaCode': '', 'status': '1', 'wfName': '人员社保证明下载'}, {'creatTime': '2025-02-28 14:09:04', 'errorReason': '', 'failType': '', 'orderNumber': 'ZJ20241225271627', 'robotName': '张弘玺', 'rpaCode': '', 'status': '1', 'wfName': '业务解构机器人【自动】'}, {'creatTime': '2025-02-28 14:04:58', 'errorReason': '', 'failType': '', 'orderNumber': 'ZJ20250114252600', 'robotName': '张弘玺', 'rpaCode': '', 'status': '1', 'wfName': '业务解构机器人【自动】'}, {'creatTime': '2025-02-28 14:04:25', 'errorReason': '', 'failType': '', 'orderNumber': 'ZJ20250110964409', 'robotName': '张弘玺', 'rpaCode': '', 'status': '1', 'wfName': '业务解构机器人【自动】'}, {'creatTime': '2025-02-28 13:54:05', 'errorReason': '', 'failType': '', 'orderNumber': 'ZJ20241113246896', 'robotName': '张弘玺', 'rpaCode': '', 'status': '1', 'wfName': '业务解构机器人【自动】'}, {'creatTime': '2025-02-28 12:15:16', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA230300009', 'robotName': 'zhoujuej.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务确认【托管】'}, {'creatTime': '2025-02-28 12:12:23', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA241100138', 'robotName': 'zhangh.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务确认【托管】'}, {'creatTime': '2025-02-27 19:01:22', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022611256256', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 19:01:06', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022711285931', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 19:00:50', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022711279325', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 19:00:31', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022711287245', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 17:15:05', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA240200026', 'robotName': 'zhoujuej.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务确认【托管】'}, {'creatTime': '2025-02-27 17:14:30', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA241100081', 'robotName': 'wuchaoq.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务确认【托管】'}, {'creatTime': '2025-02-27 17:03:10', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411215610', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 17:02:51', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022611264569', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 17:02:35', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411216040', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 17:02:20', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411216042', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 17:02:04', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411215985', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 17:01:48', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411216208', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 17:01:32', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411216048', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 17:01:16', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022711276604', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 17:01:00', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022111182001', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 17:00:45', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022311199761', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 17:00:29', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411215605', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 16:52:40', 'errorReason': '', 'failType': '', 'orderNumber': '71711266', 'orderTitle': '工作联系单:廖小琼(翼盾安全事业部)发出工作联系单', 'robotName': '', 'rpaCode': '', 'status': '1', 'wfName': '人员社保证明下载'}, {'creatTime': '2025-02-27 12:21:33', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232615', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:21:17', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232615', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:21:01', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232615', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:20:46', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232615', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:20:30', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232615', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:20:14', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232615', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:19:59', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232615', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:19:43', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232615', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:19:27', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232901', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:19:11', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232901', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:18:56', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232901', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:18:40', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232901', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:18:24', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232901', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:18:08', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232901', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:17:53', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232901', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:17:37', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232901', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:17:21', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232901', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:17:05', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232901', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:16:50', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232595', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:16:34', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232595', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:16:18', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232595', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:16:02', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232595', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:15:46', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232595', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:15:30', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232595', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:15:15', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232595', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:14:59', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232595', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:14:43', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232595', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:14:27', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232595', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:14:11', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236207', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:13:55', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236207', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:13:39', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236207', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:13:23', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236207', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:13:08', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236207', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:12:52', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236207', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:12:36', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236207', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:12:20', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236207', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:12:04', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236207', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:11:48', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236207', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:11:32', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236369', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:11:16', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236369', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:11:00', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236369', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:10:44', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236369', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:10:28', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236369', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:10:12', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236369', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:09:56', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236369', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:09:40', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236369', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:09:24', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236369', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:09:08', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236369', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:08:52', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236473', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:08:36', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236473', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:08:20', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236473', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:08:04', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236473', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:07:48', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236473', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:07:32', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236473', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:07:16', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236473', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:07:00', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236473', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:06:44', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236473', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:06:28', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236473', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:06:12', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236280', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:05:56', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236280', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:05:40', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236280', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:05:24', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236280', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:05:08', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236280', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:04:52', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236280', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:04:36', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236280', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:04:20', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236280', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:04:04', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236280', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:03:48', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236280', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:03:32', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236552', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:03:15', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236552', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:02:59', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236552', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:02:43', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236552', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:02:27', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236552', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:02:11', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511236552', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:01:55', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511244026', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:01:39', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022611250284', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:01:23', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022611250284', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:01:07', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022611263950', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:00:51', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511239291', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 12:00:35', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511239291', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 11:07:49', 'errorReason': '', 'failType': '', 'orderNumber': '张佳红-ZJXCA2500204CGN00-【2025】ZJ02375号-6%', 'robotName': '', 'rpaCode': '', 'status': '1', 'wfName': '后向合同拟稿'}, {'creatTime': '2025-02-27 11:00:47', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022611250226', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 11:00:31', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511232439', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-27 10:34:26', 'errorReason': '', 'failType': '', 'orderNumber': '202502566', 'robotName': '陈婉君', 'rpaCode': '', 'status': '1', 'wfName': '开票'}, {'creatTime': '2025-02-27 10:29:04', 'errorReason': '创建草拟合同失败，详细信息请查看日志', 'failType': '2', 'orderNumber': '张佳红-ZJXCA2500204CGN00-【2025】ZJ02375号-6%', 'robotName': '', 'rpaCode': '', 'status': '0', 'wfName': '后向合同拟稿'}, {'creatTime': '2025-02-27 10:12:24', 'errorReason': '', 'failType': '', 'orderNumber': 'ICTH33000718020044', 'robotName': 'wangbing.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务确认【托管】'}, {'creatTime': '2025-02-27 09:58:34', 'errorReason': '创建草拟合同失败，详细信息请查看日志', 'failType': '2', 'orderNumber': '张佳红-ZJXCA2500204CGN00-【2025】ZJ02375号-6%', 'robotName': '', 'rpaCode': '', 'status': '0', 'wfName': '后向合同拟稿'}, {'creatTime': '2025-02-27 07:59:55', 'errorReason': '', 'failType': '', 'orderNumber': 'ZJ20250121867876', 'robotName': '张弘玺', 'rpaCode': '', 'status': '1', 'wfName': '业务解构机器人【自动】'}, {'creatTime': '2025-02-26 22:01:06', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022611264661', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 22:00:47', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022011158338', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 22:00:32', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411216810', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 20:02:24', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411209845', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 20:02:05', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411218379', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 20:01:50', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411218574', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 20:01:34', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411215954', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 20:01:18', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411211318', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 20:01:03', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411220543', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 20:00:47', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022611256542', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 20:00:31', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022611250407', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 19:41:50', 'errorReason': '', 'failType': '', 'orderNumber': 'ICTH33000718020044', 'robotName': 'wangbing.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务发起【托管】'}, {'creatTime': '2025-02-26 19:01:04', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022111185282', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 19:00:48', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411217765', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 19:00:32', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411217652', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 17:13:34', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA241000062', 'robotName': 'chenwj.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务确认【托管】'}, {'creatTime': '2025-02-26 17:00:46', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511243389', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 17:00:30', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511244163', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 15:47:02', 'errorReason': '', 'failType': '', 'orderNumber': '71487134', 'orderTitle': '工作联系单:肖明(信产本部温州)发出工作联系单', 'robotName': '', 'rpaCode': '', 'status': '1', 'wfName': '人员社保证明下载'}, {'creatTime': '2025-02-26 10:52:22', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA250200031', 'robotName': '吴超群', 'rpaCode': 'project_initiation_approve', 'status': '1', 'wfName': '项目立项审批'}, {'creatTime': '2025-02-26 10:12:52', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA241100138', 'robotName': 'zhangh.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务确认【托管】'}, {'creatTime': '2025-02-26 09:12:29', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA221100093', 'robotName': 'wangbing.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务确认【托管】'}, {'creatTime': '2025-02-26 09:00:33', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022511228636', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-26 04:36:18', 'errorReason': '处理完成', 'failType': '', 'orderNumber': 'ZJ20240425632816', 'robotName': '', 'rpaCode': '', 'status': '1', 'wfName': '项目归集(PMS)'}, {'creatTime': '2025-02-25 19:54:45', 'errorReason': '', 'failType': '', 'orderNumber': 'ZJ20250103030227', 'robotName': '张弘玺', 'rpaCode': '', 'status': '1', 'wfName': '业务解构机器人【自动】'}, {'creatTime': '2025-02-25 19:12:39', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA250200024', 'robotName': '吴超群', 'rpaCode': 'project_initiation_approve', 'status': '1', 'wfName': '项目立项审批'}, {'creatTime': '2025-02-25 18:41:48', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA221100093', 'robotName': 'wangbing.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务发起【托管】'}, {'creatTime': '2025-02-25 17:10:39', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA230500040', 'robotName': 'zhangzh.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务确认【托管】'}, {'creatTime': '2025-02-25 16:43:39', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA241100124', 'robotName': 'chenwj.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务发起【托管】'}, {'creatTime': '2025-02-25 11:03:12', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022111182644', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-25 11:02:56', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022111182662', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-25 11:02:41', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022011161823', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-25 11:02:25', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411217970', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-25 11:02:09', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411217440', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-25 11:01:54', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411217440', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-25 11:01:38', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022411219289', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-25 11:01:22', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025021911139367', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-25 11:01:03', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025021811115801', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-25 11:00:47', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025021911141058', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-25 11:00:32', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025021911142497', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-25 10:34:28', 'errorReason': '批量开票数据填充失败：EDA客户信息【中国电信股份有限公司温州分公司】与 OA-付款单位:【中国电信股份有限公司瑞安分公司】不一致', 'failType': '1', 'orderNumber': '202502501', 'robotName': '颜瑜琼', 'rpaCode': '', 'status': '0', 'wfName': '开票'}, {'creatTime': '2025-02-25 10:30:49', 'errorReason': '', 'failType': '', 'orderNumber': '202502483', 'robotName': '陈婉君', 'rpaCode': '', 'status': '1', 'wfName': '开票'}, {'creatTime': '2025-02-25 10:28:13', 'errorReason': '批量开票数据填充失败：EDA客户信息【中国电信股份有限公司温州分公司】与 OA-付款单位:【中国电信股份有限公司瑞安分公司】不一致', 'failType': '1', 'orderNumber': '202502501', 'robotName': '颜瑜琼', 'rpaCode': '', 'status': '0', 'wfName': '开票'}, {'creatTime': '2025-02-25 10:11:53', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA220200033', 'robotName': 'wangbing.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务确认【托管】'}, {'creatTime': '2025-02-25 09:51:00', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA250100048', 'robotName': '张筝', 'rpaCode': 'project_initiation_approve', 'status': '1', 'wfName': '项目立项审批'}, {'creatTime': '2025-02-25 09:12:41', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA250200029', 'robotName': '吴超群', 'rpaCode': 'project_initiation_approve', 'status': '1', 'wfName': '项目立项审批'}, {'creatTime': '2025-02-25 04:35:10', 'errorReason': '处理完成', 'failType': '', 'orderNumber': 'ZJ20240621762766', 'robotName': '', 'rpaCode': '', 'status': '1', 'wfName': '项目归集(PMS)'}, {'creatTime': '2025-02-24 19:42:33', 'errorReason': '', 'failType': '', 'orderNumber': 'ZJ20250117452228', 'robotName': '张弘玺', 'rpaCode': '', 'status': '1', 'wfName': '业务解构机器人【自动】'}, {'creatTime': '2025-02-24 17:53:57', 'errorReason': '批量开票数据填充失败：could not convert string to float: ''', 'failType': '1', 'orderNumber': '202502499', 'robotName': '荆晶', 'rpaCode': '', 'status': '0', 'wfName': '开票'}, {'creatTime': '2025-02-24 17:14:45', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA240400031', 'robotName': 'wuchaoq.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务确认【托管】'}, {'creatTime': '2025-02-24 17:11:26', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA241200010', 'robotName': 'lourr.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务确认【托管】'}, {'creatTime': '2025-02-24 16:40:54', 'errorReason': '', 'failType': '', 'orderNumber': 'ZJDD202502240206', 'robotName': '', 'rpaCode': '', 'status': '1', 'wfName': '履约计划订单付款'}, {'creatTime': '2025-02-24 16:10:35', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJSGS240400003', 'robotName': 'zhangzh.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务确认【托管】'}, {'creatTime': '2025-02-24 16:00:48', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022111177143', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-24 16:00:33', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025022111176796', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-24 15:48:11', 'errorReason': '', 'failType': '', 'orderNumber': '202502480', 'robotName': '颜瑜琼', 'rpaCode': '', 'status': '1', 'wfName': '开票'}, {'creatTime': '2025-02-24 15:39:45', 'errorReason': '', 'failType': '', 'orderNumber': '202502475', 'robotName': '颜瑜琼', 'rpaCode': '', 'status': '1', 'wfName': '开票'}, {'creatTime': '2025-02-24 15:37:31', 'errorReason': '批量开票数据填充失败：单价不能为空，数量[], 金额[1269540.22]', 'failType': '1', 'orderNumber': '202502480', 'robotName': '颜瑜琼', 'rpaCode': '', 'status': '0', 'wfName': '开票'}, {'creatTime': '2025-02-24 15:14:46', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA241100081', 'robotName': 'wuchaoq.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务确认【托管】'}, {'creatTime': '2025-02-24 15:14:35', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA240900012', 'robotName': 'wuchaoq.xc', 'rpaCode': 'payment_plan_approve', 'status': '1', 'wfName': '收款计划调整商务确认【托管】'}, {'creatTime': '2025-02-24 15:12:08', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA250200002', 'robotName': '吴超群', 'rpaCode': 'project_initiation_approve', 'status': '1', 'wfName': '项目立项审批'}, {'creatTime': '2025-02-24 15:01:51', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025021911144623', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-24 15:01:36', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025021911144622', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-24 15:01:20', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025021911144602', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-24 15:01:04', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025021911144581', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-24 15:00:48', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025021911144232', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-24 15:00:33', 'errorReason': '', 'failType': '', 'orderNumber': 'QKEDH330025021911144481', 'robotName': 'wanghz.xc', 'rpaCode': 'revenue_audit', 'status': '1', 'wfName': '营收稽核结转报账【托管】'}, {'creatTime': '2025-02-24 14:51:01', 'errorReason': '', 'failType': '', 'orderNumber': 'XYJAZJXCA250100058', 'robotName': '张筝', 'rpaCode': 'project_initiation_approve', 'status': '1', 'wfName': '项目立项审批'}, {'creatTime': '2025-02-24 11:33:18', 'errorReason': '上传批量模板失败： 导入完成，请根据失败原因修改导入模板后重新上传！ ', 'failType': '1', 'orderNumber': '202502416', 'robotName': '吴超群', 'rpaCode': '', 'status': '0', 'wfName': '开票'}, {'creatTime': '2025-02-24 11:32:43', 'errorReason': '', 'failType': '', 'orderNumber': '202502462', 'robotName': '荆晶', 'rpaCode': '', 'status': '1', 'wfName': '开票'}, {'creatTime': '2025-02-24 11:31:58', 'errorReason': '', 'failType': '', 'orderNumber': '202502434', 'robotName': '荆晶', 'rpaCode': '', 'status': '1', 'wfName': '开票'}]