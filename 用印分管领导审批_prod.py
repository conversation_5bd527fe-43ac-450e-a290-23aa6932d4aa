# 第三方模块，作用域：操作系统进行交互的模块
import os
# 第三方模块，作用域：传递部分参数
import sys
# 第三方模块，作用域：日期、时间
import time
# 第三方模块，作用域：表格处理
import pandas as pd
# 第三方模块，作用域：图片解析
import ddddocr

import re
import random
import shutil
import datetime

# 装饰器，作用域：执行状态更改，平台部分校验通过
from merakwebroombiz.aop.api_aop import tasks_decorator

# 日志组件：1.执行步骤
from merakwebroombiz.logger.output_logger import output_logger as logger
# 需要把1.33.0升级成1.49.0
from playwright.sync_api import sync_playwright
from cryptography.fernet import Fernet
from sqlalchemy import create_engine, text
from urllib.parse import quote
from openpyxl import load_workbook
import pymysql
# 御龙测试环境
from Crypto.Cipher import AES
import binascii
from Crypto.Util.Padding import pad, unpad
import rpa_util

key = b'qK23mUVUQ_3zNMEZcSCUfYXku29QT7AXxuggFfa-gFo='
cipher_suite = Fernet(key)
# 将pymysql注册为MySQL的适配器
pymysql.install_as_MySQLdb()


# 全局变量,用于上报数据
record_id = ''  # Id:应用工作流唯一ID
server_url = 'https://yl.public.zj.cn/automation-workflow/api/' # url:服务端地址
flow_para = ''  # 参数
wf_id = '1888142875071459330'  # 流程部署ID
wf_name = '用印分管领导审批'  # 流程名
rpa_code = 'use_seal_approve'
# 用于匹配数据库信息
robot_id = '7'


class AESUtil:
    # 测试环境秘钥：16a41a967938d6428a89e5dca5350ae2
    # 正式环境秘钥：********************************
    aes_key = '********************************'

    def __init__(self, aes_key=''):
        if aes_key:
            self.aes_key = aes_key
        pass

    def encrypt(self, text):
        key = binascii.unhexlify(self.aes_key)
        cipher = AES.new(key, AES.MODE_ECB)  # 使用CBC模式，设置密钥和初始向量
        padded_text = pad(text.encode(), AES.block_size)  # 进行填充
        ciphertext = cipher.encrypt(padded_text)
        return binascii.hexlify(ciphertext).decode()

    def decrypt(self, encryptText):
        key = binascii.unhexlify(self.aes_key)
        cipher = AES.new(key, AES.MODE_ECB)
        ciphertext = binascii.unhexlify(encryptText)
        plaintext = cipher.decrypt(ciphertext)
        unpadded_plaintext = unpad(plaintext, AES.block_size)  # 去除填充
        return unpadded_plaintext.decode()

aes_util = AESUtil()


def get_sqlalchemy_engine():
    # 获取数据库连接池引擎
    # 御龙线上库，只能服务器连接
    host = '*************'
    user = 'pubinfowx'
    password = 'P5xdp#yt6'
    database = 'pubinfo_digital'
    port = '6606'
    encoded_password = quote(password)
    SQLALCHEMY_DATABASE_URI = f'mysql://{user}:{encoded_password}@{host}:{port}/{database}'
    engine = create_engine(SQLALCHEMY_DATABASE_URI, pool_size=20, max_overflow=50)
    return engine


def get_sql_result(sql, **kwargs):
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    num = 3
    while num > 0:
        try:
            with engine.connect() as connection:
                result = connection.execute(text(sql), parameters=kwargs)
                rows = result.fetchall()
            break
        except Exception as e:
            print(e)
            num -= 1
    return rows


def execute_sql(sql, **kwargs):
    sql = sql.replace(':', r'\:')
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    num = 3
    while num > 0:
        try:
            with engine.connect() as connection:
                connection.execute(text(sql), parameters=kwargs)
                connection.commit()
            break
        except Exception as e:
            print(e)
            num -= 1

    return True


def get_platform_account_from_rpa(code, type='5'):
    sql = f"SELECT a.`id`, a.`platform_login_name`, a.`platform_password` FROM pub_platform_account AS a JOIN pub_user AS b JOIN rpa_process_user AS c WHERE a.type='{type}' AND (a.is_password_right is null or a.is_password_right=1) AND a.user_id=b.id AND b.id = c.user_id and c.rpa_info_code='{code}' and c.is_enable=1"
    res = get_sql_result(sql)
    user_list = []
    if res:
        for row in res:
            nid, platform_login_name, encrypted_platform_password = row[0], row[1], row[2]
            decrypted_platform_password = aes_util.decrypt(encrypted_platform_password)
            user_list.append([platform_login_name, decrypted_platform_password, nid])
    return user_list

def update_is_password_right_2(nid):
    sql = f"update pub_platform_account set is_password_right=2 where id={nid}"
    execute_sql(sql)
    return True


def code(element):
    """
    读取图片验证码
    """
    element.screenshot(path=r'C:\Users\<USER>\Desktop\unzip\code.jpg')
    file_path = r'C:\Users\<USER>\Desktop\unzip\code.jpg'
    ocr_v = ddddocr.DdddOcr(show_ad=False)
    with open(file_path, 'rb') as file:
        # 读取图片二进制流
        img_bytes_v = file.read()
    # 识别验证码
    result_v = ocr_v.classification(img_bytes_v)
    # print(result_v)
    return result_v


def login_yu_by_account(page,account,password):
    """
    御龙平台账号密码登录
    """
    # 谷歌限制ip登录，火狐可以
    # page.goto("https://**************/pubinfo/#/login")
    page.goto("https://yl.public.zj.cn/oa/#/login")
    # 御龙测试环境
    # page.goto("http://***********:8001/pubinfo/#/login")
    page.wait_for_load_state()
    visible = page.locator("[id=\"advancedButton\"]").is_visible()
    if visible:
        page.locator("[id=\"advancedButton\"]").click()
        page.locator("[id=\"exceptionDialogButton\"]").click()
        page.wait_for_load_state()
    page.get_by_text("账号登录").click()
    time.sleep(1)
    page.fill("#form_item_account", account)
    page.fill("#form_item_password", password)
    element = page.locator("form img")
    # 获取并填写图片验证码
    page.fill("#form_item_captchas", code(element))
    # page.fill("#form_item_captchas", '1234')
    # 执行登录
    page.get_by_role("button", name="登 录").click()
    # 正确提示框  登录成功
    # message = page.locator(
    #     'div.ant-message-notice-content > div.ant-message-custom-content.ant-message-success > span:nth-child(2)')
    # 错误提示框
    message = page.locator('div.ant-message-notice-content > div.ant-message-custom-content.ant-message-error > span:nth-child(2)')
    time.sleep(1)
    if message.is_visible():
        print(f'错误提示：{message.text_content()}')
        print('重新登录')
        element = page.locator("form img")
        page.fill("#form_item_captchas", code(element))
        page.get_by_role("button", name="登 录").click()

    page.wait_for_load_state()
    return page


@tasks_decorator
def run(playwright, path=None, id=None, url=None):
    if path:
        # 使用平台传入浏览器前缀 统一路径
        executable_path = path + r'\Lib\site-packages\ms-playwright\chromium-1148\chrome-win\chrome.exe'
    else:
        # 使用本机绝对路径
        executable_path = r'C:\Users\<USER>\AppData\Local\Programs\HyperActuator\resources\Python39\Lib\site-packages\ms-playwright\chromium-1148\chrome-win\chrome.exe'
        # executable_path = r'C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1148\chrome-win\chrome.exe'
        if not os.path.exists(executable_path):  # 判断释放存在文件
            logger.info(
                '请检查本机驱动路径是否存在，处理方案：桌面右键打开【助手/执行器】文件所在位置，替换executable_path参数的前缀后，重新运行！')
            return

    account_info = get_platform_account_from_rpa(rpa_code, 4)
    logger.info(f'{len(account_info)}个用户启用了')

    for info in account_info:
        # encrypted_msg = info[1]
        # decrypted_msg = cipher_suite.decrypt(encrypted_msg).decode('utf-8')
        # 初始化浏览器对象 headless：False有头模式、True无头模式，executable_path浏览器路径
        # browser = playwright.firefox.launch(headless=False, executable_path=executable_path)
        browser = playwright.chromium.launch(headless=False, executable_path=executable_path)
        # 创建下上文环境，作用域：运行过程只要结束掉 则会关掉浏览器 释放内存
        context = browser.new_context()
        # 实例化页面对象 新建浏览器窗口页面
        page = context.new_page()

        # 跳转对应浏览器地址并登录
        print(info[0], info[1])
        page = login_yu_by_account(page, info[0], info[1])

        message = page.locator('div.ant-message-notice-content > div.ant-message-custom-content.ant-message-error > span:nth-child(2)')
        time.sleep(1)
        if message.is_visible():
            if '密码错误' in message.text_content():
                update_is_password_right_2(info[2])
                continue
        # eip_login(page, info["账号"], info["密码"])
        time.sleep(2)
        try:
            page.get_by_title("日常管理").click()
        except:
            page.get_by_text("更多应用").click()
            page.get_by_text("日常管理").nth(1).click()
        page.get_by_text("日常审批").click()
        page.get_by_text("用印管理").click()
        page.get_by_text("用印申请").click()
        page.get_by_role("tab", name="待审批", exact=False).click()
        try:
            page.locator("span").filter(has_text="条/页").click()
        except:
            continue
        page.get_by_text("100 条/页").click()
        page.get_by_role("button", name="展开 down").click()
        page.get_by_label("用印分类").fill("其他材料")
        page.get_by_role("button", name="搜 索").click()
        time.sleep(1)
        has_deal = []
        total_text = page.locator('//*[@id="app-main"]/div/div[3]/div[2]/div[2]/div/div/div[3]/div[2]/div/div/div/ul/li[1]')
        try:
            total_int = int(re.findall(r'\d+', total_text.text_content())[0])
        except:
            total_int = 0
        dt_begin = datetime.datetime.now()
        while len(has_deal) != total_int and (datetime.datetime.now() - dt_begin).seconds < 300:
            # 审批通过之后页面会刷新，所以需要重新获取items
            time.sleep(1)
            items = page.get_by_role("cell", name="审批 详情").all()
            for index, item in enumerate(items):
                time.sleep(1)
                start_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                row = page.locator(
                    f'//*[@id="app-main"]/div/div[3]/div[2]/div[2]/div/div/div[3]/div[2]/div/div/div/div/div/div[2]/table/tbody/tr[{index+2}]')
                serial_number = row.locator('td:nth-child(1)').text_content()
                title_text = row.locator('td:nth-child(2)').text_content()
                type_seal = row.locator('td:nth-child(3)').text_content()
                print(serial_number, type_seal)
                if serial_number in has_deal:
                    continue
                if type_seal != '其他材料':
                    has_deal.append(serial_number)
                    continue
                text_all = []
                item.locator('a').first.click()
                page.wait_for_load_state('networkidle')
                time.sleep(2)
                type_for_seal = page.locator(
                    '//*[@id="app-main"]/div/div[3]/div[2]/div[2]/div/div[1]/div/form[1]/div/div[3]/div/div/div[2]/div/div').text_content()
                if type_for_seal != '其他材料':
                    page.get_by_role("button", name="取 消").click()
                    print(f'{serial_number}类型不匹配:{type_for_seal}，请核实')
                    continue
                reason_for_seal = page.locator('//*[@id="app-main"]/div/div[3]/div[2]/div[2]/div/div[1]/div/form[1]/div/div[8]/div/div/div[2]/div/div').text_content()
                text_all.append(reason_for_seal)
                attachment_name_list = page.locator('//*[@id="app-main"]/div/div[3]/div[2]/div[2]/div/div[1]/div/form[1]/div/div[9]/div/div/div[2]/div/div/div/div/div').all()
                for name in attachment_name_list:
                    attachment_name = name.text_content()
                    text_all.append(attachment_name)
                print(text_all)

                status = True
                for i in text_all:
                    if '承诺' in i or '担保' in i:
                        status = False
                        break
                # time.sleep(5)
                if status:
                    print('审核通过')
                    page.get_by_label("通过", exact=True).check()
                    page.get_by_role("button", name="确 定").click()
                    # 数据上报
                    biz_no = serial_number
                    user_name = info[0]
                    end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    logger.info(f"{info[0]}:{title_text} 确认")
                    rpa_util.data_report(biz_no, '1', start_time, end_time, wf_name, wf_id, server_url,
                                         user_name, rpaCode=rpa_code)
                    # 审核通过会刷新待办
                    break
                else:
                    print('审核不通过，人工处理')
                    page.get_by_role("button", name="取 消").click()
                    has_deal.append(serial_number)
                print('---------------------------')
            # 重新计数
            print('--------------------------------------------------------')
            # try:
            #     total_text = page.locator(
            #         '//*[@id="app-main"]/div/div[3]/div[2]/div[2]/div/div/div[3]/div[2]/div/div/div/ul/li[1]')
            #     total_int = int(re.findall(r'\d+', total_text.text_content())[0])
            # except:
            #     total_int = 0


        time.sleep(2)
        try:
            context.close()
            browser.close()
        except:
            pass



if __name__ == '__main__':
    # 本地:获取当前文件夹 所在的本机绝对路径  平台:文件路径、浏览器路径、应用工作流唯一ID、服务端地址
    ley_list = sys.argv
    with sync_playwright() as playwright:
        if len(ley_list) > 1:
            path = ley_list[1]  # path:浏览器前缀统一路径
            id = ley_list[2]  # Id:应用工作流唯一ID
            url = ley_list[3]  # url:服务端地址
            run(playwright, path, id, url)
        else:
            # 本地执行
            run(playwright)