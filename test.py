import difflib


def find_longest_common_subsequence(str1, str2):
    """
    找出两个字符串的最长公共子序列，并将其拼接成一个新的字符串。
    :param str1: 第一个字符串
    :param str2: 第二个字符串
    :return: 最长公共子序列组成的字符串
    """
    # 创建SequenceMatcher对象，用于比较两个字符串
    matcher = difflib.SequenceMatcher(None, str1, str2)

    # 获取最长公共子序列的匹配信息
    match = matcher.find_longest_match(0, len(str1), 0, len(str2))

    # 如果找到匹配，则返回公共子序列；否则返回空字符串
    if match.size > 0:
        return str1[match.a: match.a + match.size]
    else:
        return ""

# 定义原始字符串
a = '防溺水监管平台支撑研发服务项目收回投标保证金22元，其中9728元转中标服务费，648500*1.5%=9728元'
b = '付甘肃省通信产业服务有限公司招标咨询分公司（防溺水监管平台支撑研发）9750元'

# 创建Differ对象
d = difflib.Differ()

# 清洗字符串，去除数字和无关描述，保留核心信息
a_cleaned = ''.join([char for char in a if not char.isdigit() and char not in ['元', '%']])
b_cleaned = ''.join([char for char in b if not char.isdigit() and char not in ['元']])

# 调用compare方法并处理结果
result = list(d.compare(a_cleaned, b_cleaned))

# 打印结果
for line in result:
    # 对比结果中的每一行进行打印，可以根据需要进一步处理这些结果
    print(line)

# 注释：此处的代码将清洗后的字符串进行比较，并通过打印结果来展示两字符串的差异。
#       由于Differ返回的是一个迭代器，我们将其转换为列表以方便查看。
common_subsequence = find_longest_common_subsequence(a, b)

# 输出结果
print("最长公共子序列:", common_subsequence)