import base64
import json
import hashlib
import sys

import pymysql
import requests
from datetime import datetime, timedelta
from playwright.sync_api import sync_playwright
# 装饰器，作用域：执行状态更改，平台部分校验通过
from merakwebroombiz.aop.api_aop import tasks_decorator
# 日志组件：1.执行步骤
from merakwebroombiz.logger.output_logger import output_logger as logger

# 获取今天的日期
now = datetime.now()
start_time = now - timedelta(days=1)
start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
end_time_str = now.strftime('%Y-%m-%d %H:%M:%S')
BASE_URL = "https://yl.public.zj.cn/pubinfo-hr"  # 生产环境地址
# BASE_URL = "http://***********:8001/pubinfo-hr"  # 测试环境地址
APP_KEY = "rpaMobileapi"
APP_SECRET = "KM3zXylxM3sdsfg7vNacvFoL5v6EPEcAux"
SALT = "WSqjgaGTJCbTgbG7Ftv0fzAcv"
is_seed = 0
event_dir = r"C:\Users\<USER>\Desktop\Template\event.json"
# 正式
# host = '*************'
# user = 'pubinfowx'
# password = 'P5xdp#yt6'
# database = 'pubinfo_digital'
# port = 6606

# 御龙测试库
host = '**************'
user = 'root'
password = 'Q/>l26yxzm5e'
database = 'pubinfo_digital'
port = 3306


def read_event_data():
    try:
        with open(event_dir, "r", encoding="utf-8") as file:
            return json.load(file)
    except:
        return []


def update_event_data(pro_orders):
    with open(event_dir, "w", encoding="utf-8") as file:
        json.dump(pro_orders, file, ensure_ascii=False, indent=4)


def get_db_connection():
    return pymysql.connect(
        host=host,  # 数据库地址
        user=user,  # 数据库用户名
        password=password,  # 数据库密码
        database=database,  # 数据库名称
        charset='utf8mb4',
        port=port
    )


def sha256_with_salt(password, salt):
    digest = hashlib.sha256()
    digest.update(salt.encode())  # 先加盐
    digest.update(password.encode())  # 再加待加密字符串
    hashed_bytes = digest.digest()
    return base64.b64encode(hashed_bytes).decode()


def send_request(endpoint, params, get_sing, json_format=False):
    url = f"{BASE_URL}{endpoint}"
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    play_load = json.dumps(params) if json_format else params  # JSON 请求需要转换
    signature = get_sing(play_load, timestamp)

    headers = {
        "Content-Type": "application/json;charset=utf-8" if json_format else "application/x-www-form-urlencoded",
        "timeStamp": timestamp,
        "appKey": APP_KEY,
        "sign": signature
    }

    # 发送请求
    response = requests.post(url, headers=headers, data=play_load)
    try:
        return response.json()
    except json.JSONDecodeError:
        return response.text


def generate_signature(data, timestamp):
    sb = []
    sb.append(APP_SECRET)
    sb.append(f"timeStamp{timestamp}")
    sb.append(f"appKey{APP_KEY}")
    sb.append(APP_SECRET)
    singstring = "".join(sb)
    signature = sha256_with_salt(singstring, SALT)
    return signature


def send_sms(phone_list, content):
    logger.info(f"短信发送成功，号码：{phone_list}, 短信内容：{content}")
    endpoint = "/open/api/message/sendSms"
    params = {
        "phones": phone_list,
        "content": content
    }
    return send_request(endpoint, params, generate_signature, json_format=True)


def insert_order_log(order_data):
    con = get_db_connection()
    try:
        with con.cursor() as cursor:
            sql = """
                INSERT INTO rpa_process_log (rpa_code, user_id, work_code, tittle, log_level, log_info, log_prompt, create_time, dispose, is_send)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (
                order_data['rpaCode'],
                order_data['robotName'],
                order_data['orderNumber'],
                order_data['orderTitle'],
                order_data['failType'],
                order_data['errorReason'],
                order_data['failType'],
                order_data['creatTime'],
                order_data['status'],
                order_data['is_seed']
            ))
            con.commit()
    except Exception as e:
        print(f"数据库操作失败: {e}")
        con.rollback()  # 回滚事务，防止部分插入
    finally:
        con.close()



def check_phone(order_data):
    con = get_db_connection()
    try:
        # 创建游标对象
        with con.cursor() as cursor:
            # SQL 查询
            sql = """
            SELECT u.mobile
            FROM pub_platform_account p
            JOIN pub_user u ON p.user_id = u.id
            WHERE p.platform_login_name = %s;
            """

            # 执行查询
            cursor.execute(sql, (order_data,))

            # 获取查询结果
            result = cursor.fetchone()  # 获取单条结果
            if result:
                return result[0]
            else:
                return None
    finally:
        # 关闭数据库连接
        con.close()

@tasks_decorator
def run(playwright, path=None, id=None, url=None):
    url = "https://yl.public.zj.cn/automation-workflow/oauth/token"
    params = {
        "client_id": "821f75bfbe2945fe89fed8145014aaeb",
        "grant_type": "client_credentials",
        "scope": "all"
    }

    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": f"Basic ODIxZjc1YmZiZTI5NDVmZTg5ZmVkODE0NTAxNGFhZWI6SHkzdW81UidmVA=="
    }

    # 发送请求获取 token
    response = requests.post(url, headers=headers, params=params)
    data = response.json()
    access_token = data.get("access_token")

    headers = {
        "Content-Type": "application/json; charset=utf-8",
        "authentication": f"bearer {access_token}"
    }

    url = "https://yl.public.zj.cn/automation-workflow/api/waibu/selectOrderList"

    data = {
        "pageNum": 1,
        "pageSize": 10000,
        "queryParameter": {
            "startTime": start_time_str,  # 使用当天的日期
            "endTime": end_time_str  # 使用前一天的日期
        }
    }

    response = requests.post(url, headers=headers, json=data)
    result = response.json()

    orders = result["data"]["list"]

    event_data = read_event_data()

    tongbu_count = 0
    for order in orders:
        order_number = order['orderNumber']
        creat_time = order['creatTime']
        order_dic = {'order_number': order_number, 'creat_time': creat_time}

        # 如果订单号已经处理过，则跳过
        if order_dic in event_data:
            continue
        ordername = order['robotName']
        try:
            phone = check_phone(ordername)['phone']
            uesr_id = check_phone(ordername)['uesr_id']
        except:
            phone = None
            uesr_id = None
        order['status'] = 2 - int(order['status'])
        status_dic = {1: '成功', 0: '失败'}
        fail_type = {1: '业务类失败', 2: '不可控因素失败', 3: '账号密码错误'}
        content = f"账号：{order['robotName']} 工单（{order['orderNumber']}）处理失败,失败类型：{order['failType']} 失败原因：{order['errorReason']} 时间：{order['creatTime']}"
        if order['status']== 2 and is_seed == 1 and phone:
            # content = f"账号：{order['robotName']} 工单（{order['orderNumber']}）处理失败,失败类型：{order['failType']} 失败原因：{order['errorReason']} 时间：{order['creatTime']}"
            mss = send_sms(phone, content)
            if mss['code'] != 200:
                order['is_seed'] = 2
            else:
                order['is_seed'] = 1
                logger.info(f"工单（{order['orderNumber']}）已向{phone}发送短信提醒")
        else:
            order['is_seed'] = 2
        try:
            if not order['orderTitle']:
                order['orderTitle'] = ''
        except:
            order['orderTitle'] = ''
        print("发送成功")
        # 插入到数据库
        order['uesr_id'] = uesr_id
        if order['failType'] == '':
            order['failType'] = '0'
        insert_order_log(order)
        event_data.append(order_dic)
        logger.info(f"{content} 已同步到数据库")
        tongbu_count += 1

    # 更新 event.json 文件
    update_event_data(event_data)
    logger.info(f"同步结束，共处理{len(orders)}条订单, 同步了{tongbu_count}条新订单")

if __name__ == '__main__':
    # 本地:获取当前文件夹 所在的本机绝对路径  平台:文件路径、浏览器路径、应用工作流唯一ID、服务端地址
    ley_list = sys.argv
    with sync_playwright() as playwright:
        if len(ley_list) > 1:
            path = ley_list[1]  # path:浏览器前缀统一路径
            id = ley_list[2]  # Id:应用工作流唯一ID
            url = ley_list[3]  # url:服务端地址
            run(playwright, path, id, url)
        else:
            # 本地执行
            run(playwright)
