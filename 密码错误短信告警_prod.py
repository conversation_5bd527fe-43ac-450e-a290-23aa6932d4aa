import base64
import json
import hashlib
import sys
import time

from pandas.core import resample
import pymysql
import requests
from datetime import datetime, timedelta
from playwright.sync_api import sync_playwright
# 装饰器，作用域：执行状态更改，平台部分校验通过
from merakwebroombiz.aop.api_aop import tasks_decorator
# 日志组件：1.执行步骤
from merakwebroombiz.logger.output_logger import output_logger as logger

# 获取今天的日期
now = datetime.now()
start_time = now - timedelta(days=1)
start_time_str = start_time.strftime('%Y-%m-%d %H:%M:%S')
end_time_str = now.strftime('%Y-%m-%d %H:%M:%S')
BASE_URL = "https://yl.public.zj.cn/pubinfo-hr"  # 生产环境地址
# BASE_URL = "http://***********:8001/pubinfo-hr"  # 测试环境地址
APP_KEY = "rpaMobileapi"
APP_SECRET = "KM3zXylxM3sdsfg7vNacvFoL5v6EPEcAux"
SALT = "WSqjgaGTJCbTgbG7Ftv0fzAcv"
is_seed = 1
event_dir = r"C:\Users\<USER>\Desktop\Template\event.json"
# 正式
host = '*************'
user = 'pubinfowx'
password = 'P5xdp#yt6'
database = 'pubinfo_digital'
port = 6606

# 御龙测试库
# host = '**************'
# user = 'root'
# password = 'Q/>l26yxzm5e'
# database = 'pubinfo_digital'
# port = 3306

def get_db_connection():
    return pymysql.connect(
        host=host,  # 数据库地址
        user=user,  # 数据库用户名
        password=password,  # 数据库密码
        database=database,  # 数据库名称
        charset='utf8mb4',
        port=port
    )


def sha256_with_salt(password, salt):
    digest = hashlib.sha256()
    digest.update(salt.encode())  # 先加盐
    digest.update(password.encode())  # 再加待加密字符串
    hashed_bytes = digest.digest()
    return base64.b64encode(hashed_bytes).decode()


def send_request(endpoint, params, get_sing, json_format=False):
    url = f"{BASE_URL}{endpoint}"
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    play_load = json.dumps(params) if json_format else params  # JSON 请求需要转换
    signature = get_sing(play_load, timestamp)

    headers = {
        "Content-Type": "application/json;charset=utf-8" if json_format else "application/x-www-form-urlencoded",
        "timeStamp": timestamp,
        "appKey": APP_KEY,
        "sign": signature
    }

    # 发送请求
    response = requests.post(url, headers=headers, data=play_load)
    try:
        return response.json()
    except json.JSONDecodeError:
        return response.text


def generate_signature(data, timestamp):
    sb = []
    sb.append(APP_SECRET)
    sb.append(f"timeStamp{timestamp}")
    sb.append(f"appKey{APP_KEY}")
    sb.append(APP_SECRET)
    singstring = "".join(sb)
    signature = sha256_with_salt(singstring, SALT)
    return signature


def send_sms(phone_list, content):
    logger.info(f"开始短信发送，号码：{phone_list}, 短信内容：{content}")
    endpoint = "/open/api/message/sendSms"
    params = {
        "phones": phone_list,
        "content": content
    }
    return send_request(endpoint, params, generate_signature, json_format=True)


# def get_platform_error_account():
#     sql = f"SELECT DISTINCT b.mobile, a.`id`, a.`platform_login_name`, a.`platform_password`, a.`type`, a.`is_password_right` FROM pub_platform_account AS a JOIN pub_user AS b JOIN rpa_process_user AS c WHERE a.is_password_right=2 AND a.user_id=b.id AND b.id = c.user_id AND c.is_enable=1"
#     res = get_sql_result(sql)
#     user_list = []
#     if res:
#         for row in res:
#             mobile, nid, platform_login_name, encrypted_platform_password, ntype = row[0], row[1], row[2], row[3], row[4]
#             decrypted_platform_password = aes_util.decrypt(encrypted_platform_password)
#             user_list.append([platform_login_name, decrypted_platform_password, nid, mobile, ntype])
#     return user_list


def get_platform_error_account():
    con = get_db_connection()
    try:
        # 创建游标对象
        with con.cursor() as cursor:
            # SQL 查询
            sql = f"SELECT DISTINCT b.mobile, a.`id`, a.`platform_login_name`, a.`platform_password`, a.`type`, a.`is_password_right` FROM pub_platform_account AS a JOIN pub_user AS b JOIN rpa_process_user AS c WHERE a.is_password_right=2 AND a.user_id=b.id AND b.id = c.user_id AND c.is_enable=1"
            # 执行查询
            cursor.execute(sql)
            # 获取查询结果
            res = cursor.fetchall()
            user_list = []
            if res:
                 for row in res:
                    mobile, nid, platform_login_name, encrypted_platform_password, ntype = row[0], row[1], row[2], row[3], row[4]
                    # decrypted_platform_password = aes_util.decrypt(encrypted_platform_password)
                    user_list.append([platform_login_name, encrypted_platform_password, nid, mobile, ntype])
            return user_list
    finally:
        # 关闭数据库连接
        con.close()


@tasks_decorator
def run(playwright, path=None, id=None, url=None):
    time.sleep(5)
    account_info = get_platform_error_account()
    logger.info(f'{len(account_info)}个用户密码错误')

    for info in account_info:
        type2plat = {'1': '钉钉', '2': '研发云', '3': '老OA', '4': '御龙', '5': 'EIP'}
        plat = type2plat.get(str(info[4]), '未知')
        phone = [info[3]]
        content = f"御龙平台RPA机器人存储{plat}平台账号：{info[0]} 密码错误，请及时登录御龙修改！"
        mss = send_sms(phone, content)
        if mss['code'] != 0:
            logger.info(f"向{phone}发送短信失败，错误原因: {mss}")
        else:
            logger.info(f"已向{phone}发送短信提醒: {content}")
        print("发送成功")


if __name__ == '__main__':
    # 本地:获取当前文件夹 所在的本机绝对路径  平台:文件路径、浏览器路径、应用工作流唯一ID、服务端地址
    ley_list = sys.argv
    with sync_playwright() as playwright:
        if len(ley_list) > 1:
            path = ley_list[1]  # path:浏览器前缀统一路径
            id = ley_list[2]  # Id:应用工作流唯一ID
            url = ley_list[3]  # url:服务端地址
            run(playwright, path, id, url)
        else:
            # 本地执行
            run(playwright)
