# 收入收款计划变更
# 第三方模块，作用域：操作系统进行交互的模块
import os
# 第三方模块，作用域：传递部分参数
import sys
# 第三方模块，作用域：日期、时间
import time
# 第三方模块，作用域：表格处理
import pandas as pd
import re
import random
import shutil
import datetime

# 装饰器，作用域：执行状态更改，平台部分校验通过
from merakwebroombiz.aop.api_aop import tasks_decorator

# 日志组件：1.执行步骤
from merakwebroombiz.logger.output_logger import output_logger as logger
# 需要把1.33.0升级成1.49.0
from playwright.sync_api import sync_playwright
from cryptography.fernet import Fernet
from sqlalchemy import create_engine, text
from urllib.parse import quote
from openpyxl import load_workbook
import pymysql

# 用于匹配数据库信息
robot_id = '5'
env = '待办'
lie = {'待办': '5', '已办': '3'}
sbtypes = {'待办': 's_sbtypes', '已办': 's_sbtypes_al'}


department_leaders = ['章锐', '孙锵', '周佩雷', '赵涛', '叶小卫', '方巍然', '夏巧刚', '刘李传', '沈燮勇', '王磊',
                      '何洋军', '刘辉', '梁晓', '胡金栋']

key = b'qK23mUVUQ_3zNMEZcSCUfYXku29QT7AXxuggFfa-gFo='
cipher_suite = Fernet(key)
# 将pymysql注册为MySQL的适配器
pymysql.install_as_MySQLdb()


def get_sqlalchemy_engine():
    # 获取数据库连接池引擎
    # host = '**************'
    # user = 'root'
    # password = '08@3FYm*zp+NX3%e'
    # database = 'rpa'
    # port = '8866'
    # 御龙测试库
    host = '**************'
    user = 'root'
    password = 'Q/>l26yxzm5e'
    database = 'pubinfo_digital'
    port = '3306'
    # 御龙线上库，只能服务器连接
    # host = '*************'
    # user = 'pubinfowx'
    # password = 'P5xdp#yt6'
    # database = 'pubinfo_digital'
    # port = '6606'
    encoded_password = quote(password)
    SQLALCHEMY_DATABASE_URI = f'mysql://{user}:{encoded_password}@{host}:{port}/{database}'
    engine = create_engine(SQLALCHEMY_DATABASE_URI, pool_size=20, max_overflow=50)
    return engine


def get_sql_result(sql, **kwargs):
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    with engine.connect() as connection:
        result = connection.execute(text(sql), parameters=kwargs)
        rows = result.fetchall()
    return rows


def execute_sql(sql, **kwargs):
    sql = sql.replace(':', r'\:')
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    with engine.connect() as connection:
        connection.execute(text(sql), parameters=kwargs)
        connection.commit()
    return True


def is_gt_time(d1):
    now_time = datetime.datetime.now()
    time_difference = now_time - datetime.datetime.strptime(d1, '%Y-%m-%d %H:%M:%S')
    return time_difference.total_seconds() > 300


def login_by_account(page, login, account, password):
    """
    账号密码登录
    """
    page.goto(login)
    page.wait_for_load_state()
    page.fill("#freename", account)
    page.fill("#freepassword", password)
    # 执行登录
    page.click('#login_btnDiv > input')
    page.wait_for_load_state()
    return page


def create_new_excel(tmp_excel_path, new_excel_path):
    if not os.path.isfile(tmp_excel_path):
        print('模板文件不存在')
        return False
    shutil.copy2(tmp_excel_path, new_excel_path)
    return True


# 取数结果写入附件
def report_export(contractcode, contractfee, diff1, diff2):
    tmp_excel_path = 'D:\代码文档\RPA\light-edit\Template\Template_5_1.xlsx'
    new_excel_path = fr'D:\代码文档\RPA\light-edit\Template\DataInfo\{contractcode}.xlsx'
    if os.path.exists(new_excel_path):
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        file_dir, file_name = os.path.split(new_excel_path)
        file_name_base, file_extension = os.path.splitext(file_name)
        new_file_name = f"{file_name_base}_{timestamp}{file_extension}"
        new_file_path = os.path.join(file_dir, new_file_name)
        os.rename(new_excel_path, new_file_path)
    # 将模板拷贝一份新的
    status = create_new_excel(tmp_excel_path, new_excel_path)
    if status is False:
        exit()
    # 打开新表
    new_workbook = load_workbook(new_excel_path)
    sheet = new_workbook.active
    # # 第6行插入一行，按需
    # sheet.insert_rows(6)

    # 对应单元格写入数据
    sheet['B2'] = contractcode
    sheet['D2'] = float(contractfee.replace(',', ''))
    for index, item in enumerate(diff2):
        row = 5 + index
        sheet[f'A{row}'] = item[0].replace('-', '/')
        sheet[f'B{row}'] = float(item[1].replace(',', ''))
        sheet[f'C{row}'] = float(item[2])
        sheet[f'D{row}'] = item[3]
    for index, item in enumerate(diff1):
        row = 12 + index
        sheet[f'A{row}'] = item[0].replace('-', '/')
        sheet[f'B{row}'] = float(item[1].replace(',', ''))
        sheet[f'C{row}'] = float(item[2])
        sheet[f'D{row}'] = item[3]
    new_workbook.save(new_excel_path)
    print('附件新建完毕')
    return new_excel_path


@tasks_decorator
def run(playwright, path=None, id=None, url=None):
    if path:
        # 使用平台传入浏览器前缀 统一路径
        executable_path = path + r'\Lib\site-packages\ms-playwright\firefox-1327\firefox\firefox.exe'
    else:
        # 使用本机绝对路径
        # executable_path = r'C:\Program Files\HyperActuator\resources\Python39\Lib\site-packages\ms-playwright\chromium-1060\chrome-win\chrome.exe'
        executable_path = r'C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1148\chrome-win\chrome.exe'
        if not os.path.exists(executable_path):  # 判断释放存在文件
            logger.error(
                '请检查本机驱动路径是否存在，处理方案：桌面右键打开【助手/执行器】文件所在位置，替换executable_path参数的前缀后，重新运行！')
            return

    # file_path = "D:\data\人员账号信息.xlsx"
    # account_info = load_account_info(file_path)
    sql = f"SELECT `username`,`password` FROM rpa_users WHERE robot_id='{robot_id}' AND status='Enable'"
    account_info = get_sql_result(sql)

    for info in account_info:
        encrypted_msg = info[1]
        decrypted_msg = cipher_suite.decrypt(encrypted_msg).decode('utf-8')
        # 初始化浏览器对象 headless：False有头模式、True无头模式，executable_path浏览器路径
        # browser = playwright.firefox.launch(headless=False, executable_path=executable_path)
        browser = playwright.chromium.launch(headless=False, executable_path=executable_path)
        # 创建下上文环境，作用域：运行过程只要结束掉 则会关掉浏览器 释放内存
        context = browser.new_context()
        # 实例化页面对象 新建浏览器窗口页面
        page = context.new_page()
        # 跳转对应浏览器地址并登录
        page = login_by_account(page, "http://sso.zjtelecom.cn/cas/login", info[0], decrypted_msg)
        # eip_login(page, info["账号"], info["密码"])
        time.sleep(2)
        page.get_by_text("集团应用").click()
        with page.expect_popup() as page1_info:
            page.locator("#jt_apps div").filter(has_text="市场经营 集团ICT").get_by_role("img").first.click()
        page1 = page1_info.value
        try:
            page1.wait_for_load_state(state="networkidle", timeout=10000)
        except:
            pass

        # 关闭弹窗
        page1.on("dialog", lambda dialog: dialog.dismiss())

        time.sleep(1)
        page1.locator('//*[@id="contractLeft"]/div/ul/li/ul/li/a').filter(has_text=f"我的{env}").click()
        time.sleep(2)

        row_text = page1.locator("iframe[name=\"menuiframe\"]").content_frame.locator(
            '//*[@id="DataForm"]/tbody/tr[2]/td').first.inner_text()
        if row_text == '暂无记录!':
            print('页面无数据，退出')
            continue
        time.sleep(5)
        # 业务类型选择
        page1.locator("iframe[name=\"menuiframe\"]").content_frame.locator(
            f"input[name=\"ch_value\\({sbtypes[env]}\\)\"]").click()
        page1.locator("iframe[name=\"menuiframe\"]").content_frame.locator(
            f'//*[@id="ff_value({sbtypes[env]})"]/input[@chvalue="协议级收入收款计划调整"]').check()
        # 当前办理步骤选择
        page1.locator("iframe[name=\"menuiframe\"]").content_frame.locator(
            "input[name=\"ch_value\\(s_currstatus\\)\"]").click()
        try:
            page1.locator("iframe[name=\"menuiframe\"]").content_frame.locator(
                '//*[@id="ff_value(s_currstatus)"]/input[@chvalue="发起收入收款计划变更"]').check()
        except:
            print('未找到发起收入收款计划变更，跳过当前账号')
            continue
        page1.locator("iframe[name=\"menuiframe\"]").content_frame.get_by_role("cell", name="查询", exact=True).locator(
            "a").click()

        while True:
            time.sleep(1)
            iframe_locator = page1.locator("iframe[name=\"menuiframe\"]").content_frame
            # 待办第5列，已办第3列
            links = iframe_locator.locator(f'//*[@id="DataForm"]/tbody/tr/td[{lie[env]}]/a').all()
            for num, link in enumerate(links):
                text = link.inner_text()
                print(text)

                time_delivery = iframe_locator.locator(f'//*[@id="DataForm"]/tbody/tr[{num+2}]/td[8]').inner_text()
                print(time_delivery)
                if not is_gt_time(time_delivery):
                    continue

                # stage = iframe_locator.locator(f'//*[@id="DataForm"]/tbody/tr[{num+2}]/td[3]').inner_text()
                # if stage != '发起收入收款计划变更':
                #     print(f"当前环节{stage}不是发起收入收款计划变更，跳过处理")
                #     continue

                with page1.expect_popup() as page2_info:
                    link.click()
                page2 = page2_info.value
                page2.wait_for_load_state()
                time.sleep(2)

                title = page2.locator('body > form > div.WrapMiddle > div > h1').first.inner_text()
                print(title)
                if '（执行变更）' not in title:
                    print('不是执行变更，跳过处理')
                    page2.close()
                    continue

                # 获取合同编码和金额
                contractcode = page2.locator('//*[@id="span_value(contractcode)"]').last.inner_text()
                contractfee = page2.locator('//*[@id="span_value(contractfee)"]').last.inner_text()

                # 已有附件
                hasfile = False
                filename = f'【协议级收入收款计划调整】{contractcode}.xlsx'
                ictplanchgupfile_filelist = page2.locator(
                    '//*[@id="ictplanchgupfile_filelist"]/div/span[1]/a/span').all()
                for upfile in ictplanchgupfile_filelist:
                    print(upfile.text_content())
                    if filename in upfile.text_content():
                        hasfile = True
                        break
                if hasfile:
                    print('已有同名附件，跳过处理')
                    # 提交
                    print('准备提交')
                    page2.locator("#btn1_sav_endrecover").click()
                    time.sleep(2)
                    page2.locator("iframe[name=\"ifrwindow\"]").content_frame.get_by_text("同意", exact=True).dblclick()
                    next_processors = page2.locator("iframe[name=\"ifrwindow\"]").content_frame.locator("#theusersle > table > tbody > tr:nth-child(1) > td").all()
                    has_leader = False
                    for processor in next_processors:
                        processor_name = processor.locator('label > span').get_attribute('title')
                        print(processor_name)
                        leader = processor_name.split('/')[0]
                        if leader in department_leaders:
                            processor.locator('label > i').click()
                            has_leader = True
                            break
                    if has_leader:
                        page2.locator("iframe[name=\"ifrwindow\"]").content_frame.get_by_role("link",
                                                                                              name="确认").click()
                    else:
                        page2.locator("iframe[name=\"ifrwindow\"]").content_frame.get_by_role("link",
                                                                                              name="返回表单").click()
                    page2.close()
                    continue

                # 处理页面元素
                with page2.expect_popup() as page3_info:
                    page2.get_by_role("link", name="收入计划").last.click()
                page3 = page3_info.value
                page3.wait_for_load_state()
                # 获取新变更数据，待办中是可编辑模式，已办中是只读模式，获取整个表
                # planconfirmfee_off = page3.locator('//*[@id="span_value(planconfirmfee_off)"]').last.inner_text()
                # planconfirmfee_notax_off = page3.locator('//*[@id="span_value(planconfirmfee_notax_off)"]').last.inner_text()
                # planconfirmdate_off = page3.locator('//*[@id="span_value(planconfirmdate_off)"]').last.inner_text()
                # print('变更数据：', planconfirmfee_off, planconfirmfee_notax_off, planconfirmdate_off)
                table_list = []
                TBL_OFFCYCLE = page3.locator('//*[@id="TBL_OFFCYCLE"]/tbody/tr[@sortrow=1]').all()
                for row in TBL_OFFCYCLE:
                    # 发起人状态是编辑状态
                    inputitem_off_txt = row.locator('//*[@name="value(inputitem_off_txt)"]').evaluate(
                        '(element) => element.value')
                    taxrate_off_txt = row.locator('//*[@name="value(taxrate_off)"]').evaluate(
                        '(element) => element.value')
                    planconfirmfee_off = row.locator('//*[@name="value(planconfirmfee_off)"]').evaluate(
                        '(element) => element.value')
                    planconfirmdate_off = row.locator('//*[@name="value(planconfirmdate_off)"]').evaluate(
                        '(element) => element.value')
                    # 以下是财务账号只读状态
                    # taxrate_off_txt = row.locator('//*[@id="span_value(taxrate_off_txt)"]').inner_text()
                    # planconfirmfee_off = row.locator('//*[@id="span_value(planconfirmfee_off)"]').inner_text()
                    # planconfirmfee_notax_off = row.locator(
                    #     '//*[@id="span_value(planconfirmfee_notax_off)"]').inner_text()
                    # planconfirmdate_off = row.locator('//*[@id="span_value(planconfirmdate_off)"]').inner_text()
                    table_list.append([planconfirmdate_off, planconfirmfee_off, taxrate_off_txt, inputitem_off_txt])
                print(table_list)
                page3.close()

                # 获取上一次变更信息
                original_table_list = []
                change_type = page2.locator(
                    '//*[@class="ui-tabs-panel ui-widget-content ui-corner-bottom"]/div/table/tbody/tr/td[2]/a').last.inner_text()
                # 可能是项目立项
                if change_type != '执行变更':
                    print('无法获取上一次变更信息，可能是刚立项')
                    # continue
                else:
                    # 获取上一次变更数据
                    with page2.expect_popup() as page4_info:
                        page2.locator(
                            '//*[@class="ui-tabs-panel ui-widget-content ui-corner-bottom"]/div/table/tbody/tr/td[3]/a').last.click()
                    page4 = page4_info.value
                    page4.wait_for_load_state()
                    # 处理页面元素
                    with page4.expect_popup() as page5_info:
                        page4.get_by_role("link", name="收入计划").last.click()
                    page5 = page5_info.value
                    page5.wait_for_load_state()
                    # original_planconfirmfee_off = page5.locator('//*[@id="span_value(planconfirmfee_off)"]').last.inner_text()
                    # original_planconfirmfee_notax_off = page5.locator('//*[@id="span_value(planconfirmfee_notax_off)"]').last.inner_text()
                    # original_planconfirmdate_off = page5.locator('//*[@id="span_value(planconfirmdate_off)"]').last.inner_text()
                    # print('原始数据：', original_planconfirmfee_off, original_planconfirmfee_notax_off, original_planconfirmdate_off)

                    Original_TBL_OFFCYCLE = page5.locator('//*[@id="TBL_OFFCYCLE"]/tbody/tr[@sortrow=1]').all()
                    for row in Original_TBL_OFFCYCLE:
                        original_inputitem_off_txt = row.locator(
                            '//*[@id="span_value(inputitem_off_txt)"]').inner_text()
                        original_taxrate_off_txt = row.locator('//*[@id="span_value(taxrate_off_txt)"]').inner_text()
                        original_planconfirmfee_off = row.locator(
                            '//*[@id="span_value(planconfirmfee_off)"]').inner_text()
                        original_planconfirmfee_notax_off = row.locator(
                            '//*[@id="span_value(planconfirmfee_notax_off)"]').inner_text()
                        original_planconfirmdate_off = row.locator(
                            '//*[@id="span_value(planconfirmdate_off)"]').inner_text()
                        # original_table_list.append(
                        #     [original_taxrate_off_txt, original_planconfirmfee_off, original_planconfirmfee_notax_off,
                        #      original_planconfirmdate_off])
                        original_table_list.append(
                            [original_planconfirmdate_off, original_planconfirmfee_off.replace(',', ''),
                             original_taxrate_off_txt, original_inputitem_off_txt])
                    print(original_table_list)

                    page5.close()
                    page4.close()

                diff1 = [item for item in table_list if item not in original_table_list]
                diff2 = [item for item in original_table_list if item not in table_list]
                print("取数成功")
                print(contractcode, contractfee)
                print(f'修改后{diff1}')
                print(f'修改前{diff2}')

                # 数据写入附件
                if diff1 or diff2:
                    attachment = report_export(contractcode, contractfee, diff1, diff2)
                    page2.get_by_role("link", name="上传附件").click()

                    # 使用 FileChooser 选择本地文件
                    with page2.expect_file_chooser() as fc_info:
                        page2.locator("iframe[name=\"ifrwindow\"]").content_frame.get_by_role("textbox").click()
                        file_chooser = fc_info.value
                        file_chooser.set_files(attachment)

                    time.sleep(10)

                    page2.locator("iframe[name=\"ifrwindow\"]").content_frame.get_by_role("link", name="确定").click()
                    time.sleep(1)

                # 提交
                print('准备提交')
                page2.locator("#btn1_sav_endrecover").click()
                time.sleep(2)
                page2.locator("iframe[name=\"ifrwindow\"]").content_frame.get_by_text("同意", exact=True).dblclick()
                next_processors = page2.locator("iframe[name=\"ifrwindow\"]").content_frame.locator(
                    "#theusersle > table > tbody > tr:nth-child(1) > td").all()
                has_leader = False
                for processor in next_processors:
                    processor_name = processor.locator('label > span').get_attribute('title')
                    print(processor_name)
                    leader = processor_name.split('/')[0]
                    if leader in department_leaders:
                        processor.locator('label > i').click()
                        has_leader = True
                        break
                if has_leader:
                    page2.locator("iframe[name=\"ifrwindow\"]").content_frame.get_by_role("link",
                                                                                          name="确认").click()
                else:
                    page2.locator("iframe[name=\"ifrwindow\"]").content_frame.get_by_role("link",
                                                                                          name="返回表单").click()

                page2.close()

            try:
                flag = page1.locator("iframe[name=\"menuiframe\"]").content_frame.locator(
                    "#ajaxBottomBtndiv").get_by_role(
                    "link", name="下一页")
                if flag.is_visible():
                    flag.click()
                    page1.wait_for_load_state()
                else:
                    print('没有下一页了，退出')
                    break
            except Exception as e:
                print(e)
                break

        time.sleep(2)
        try:
            context.close()
            browser.close()
        except:
            pass


if __name__ == '__main__':
    # 本地:获取当前文件夹 所在的本机绝对路径  平台:文件路径、浏览器路径、应用工作流唯一ID、服务端地址
    ley_list = sys.argv
    with sync_playwright() as playwright:
        if len(ley_list) > 1:
            path = ley_list[1]  # path:浏览器前缀统一路径
            id = ley_list[2]  # Id:应用工作流唯一ID
            url = ley_list[3]  # url:服务端地址
            run(playwright, path, id, url)
        else:
            # 本地执行
            run(playwright)
