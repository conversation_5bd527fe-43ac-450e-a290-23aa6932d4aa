from sqlalchemy import create_engine, text
from urllib.parse import quote
import pymysql

# 将pymysql注册为MySQL的适配器
pymysql.install_as_MySQLdb()

def get_sqlalchemy_engine():
    # 获取数据库连接池引擎
    host='**************'
    user='root'
    password='admin@123456'
    encoded_password = quote(password)
    database='rpa'
    port='8866'
    SQLALCHEMY_DATABASE_URI = f'mysql://{user}:{encoded_password}@{host}:{port}/{database}'
    engine = create_engine(SQLALCHEMY_DATABASE_URI, pool_size=20, max_overflow=50)
    return engine

def get_sql_result(sql, **kwargs):
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    with engine.connect() as connection:
        result = connection.execute(text(sql), parameters=kwargs)
        rows = result.fetchall()
    return rows


def execute_sql(sql, **kwargs):
    sql = sql.replace(':', r'\:')
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    with engine.connect() as connection:
        connection.execute(text(sql), parameters=kwargs)
        connection.commit()
    return True

robot_id = '1'
sql = f"SELECT `username`,`password` FROM rpa_users WHERE robot_id={robot_id} AND status='Enable'"
res = get_sql_result(sql)
print(res)


from cryptography.fernet import Fernet
key = b'qK23mUVUQ_3zNMEZcSCUfYXku29QT7AXxuggFfa-gFo='
cipher_suite = Fernet(key)
for info in res:
    encrypted_msg = info[1]
    decrypted_msg = cipher_suite.decrypt(encrypted_msg).decode('utf-8')
    print("Decrypted Message: ", decrypted_msg)

OrderNumber = '1234567'
sql = f"INSERT INTO rpa_flow (robot_id, flow_number) VALUES ({robot_id}, {OrderNumber})"
execute_sql(sql)
sql = f"SELECT `flow_number` FROM rpa_flow WHERE robot_id={robot_id}"
res = get_sql_result(sql)
print(res)
