# 第三方模块，作用域：表格处理
import pandas as pd
from cryptography.fernet import Fernet
from sqlalchemy import create_engine, text
from urllib.parse import quote
import re
import pymysql
# 用于匹配数据库信息
robot_id = '2'
# 将pymysql注册为MySQL的适配器
pymysql.install_as_MySQLdb()

def load_supplier_info(file_path, max_rows=None):
    sheet_name = '关联平台供应商'
    df = pd.read_excel(file_path, sheet_name=sheet_name, nrows=max_rows)  # sheet_name=0表示第一个工作表，也可以使用工作表名称
    data = df.to_dict('records')
    return data


def get_sqlalchemy_engine():
    # 获取数据库连接池引擎
    host='**************'
    user='root'
    password='admin@123456'
    encoded_password = quote(password)
    database='rpa'
    port='8866'
    SQLALCHEMY_DATABASE_URI = f'mysql://{user}:{encoded_password}@{host}:{port}/{database}'
    engine = create_engine(SQLALCHEMY_DATABASE_URI, pool_size=20, max_overflow=50)
    return engine


def get_sql_result(sql, **kwargs):
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    with engine.connect() as connection:
        result = connection.execute(text(sql), parameters=kwargs)
        rows = result.fetchall()
    return rows


def execute_sql(sql, **kwargs):
    sql = sql.replace(':', r'\:')
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    with engine.connect() as connection:
        connection.execute(text(sql), parameters=kwargs)
        connection.commit()
    return True


if __name__ == '__main__':
    data = load_supplier_info(r'D:\代码文档\RPA\ID机器人相关材料\2\平台客商+打标+模板.xls')
    for row in data:
        name = row['名称'].replace('"', r'\"')
        sql = f"""INSERT INTO financial_supplier (code, name) VALUES ("{row['代码']}", "{name}")"""
        print(sql)
        try:
            execute_sql(sql)
        except Exception as e:
            print(e)



