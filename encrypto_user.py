from cryptography.fernet import <PERSON><PERSON>t

# Generate a key for encryption and decryption (you should keep this safe)
# key = Fernet.generate_key()
# print("Key: ", key)
key = b'qK23mUVUQ_3zNMEZcSCUfYXku29QT7AXxuggFfa-gFo='

# Create an instance of the Fernet class with your generated key
cipher_suite = Fernet(key)

# The message you want to encrypt
message = "Ye710405@"

# Encrypt the message using the cipher suite created above
encrypted_msg = cipher_suite.encrypt(bytes(message,'utf-8')) # convert string to bytes before encryption
print("Encrypted Message: ", encrypted_msg)

# Decrypt the message back to its original form
decrypted_msg = cipher_suite.decrypt(encrypted_msg).decode('utf-8') # convert byte array to string after decryption
print("Decrypted Message: ", decrypted_msg)