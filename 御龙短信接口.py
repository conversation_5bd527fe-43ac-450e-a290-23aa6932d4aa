import hashlib
import collections
from datetime import datetime
import requests
import json
import base64


appKey = "rpaMobileapi"
appSecret = "KM3zXylxM3sdsfg7vNacvFoL5v6EPEcAux"
salt = "WSqjgaGTJCbTgbG7Ftv0fzAcv"


def sha256_with_salt(password, salt):
    digest = hashlib.sha256()
    digest.update(salt.encode())
    digest.update(password.encode())
    hashed_bytes = digest.digest()
    return base64.b64encode(hashed_bytes).decode()

def generate_signature(data, timeStamp):
    sb = []
    sb.append(appSecret)
    sb.append(f"timeStamp{timeStamp}")
    sb.append(f"appKey{appKey}")
    sb.append(appSecret)
    singString = "".join(sb)
    signature = sha256_with_salt(singString, salt)
    return signature







if __name__ == "__main__":
    params = {
        "phones": ["17764519308"],
        "content": "测试"
    }
    play_load = json.dumps(params)

    # 构建请求头
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    signature = generate_signature(play_load, timestamp)
    headers = {
        "Content-Type": "application/json;charset=utf-8",
        "timeStamp" : timestamp,
        "appKey" : appKey,
        "sign": signature
    }

    url = 'http://36.26.66.79:8001/pubinfo-hr/open/api/message/sendSms'

    response = requests.post(url, headers=headers, data=play_load)
    print(response.text)

    if response.status_code == 200:
        print("请求成功，响应内容为：", response.text)
    else:
        print(f"请求失败，状态码为：{response.status_code}")
