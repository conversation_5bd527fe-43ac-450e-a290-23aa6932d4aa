# 营收稽核结转报账

# 第三方模块，作用域：操作系统进行交互的模块
import os
# 第三方模块，作用域：传递部分参数
import sys
# 第三方模块，作用域：日期、时间
import time
# 第三方模块，作用域：表格处理
import pandas as pd
import re
import random
import rpa_util
from datetime import datetime

# 装饰器，作用域：执行状态更改，平台部分校验通过
from merakwebroombiz.aop.api_aop import tasks_decorator

# 日志组件：1.执行步骤
from merakwebroombiz.logger.output_logger import output_logger as logger
# 需要把1.33.0升级成1.49.0
from playwright.sync_api import sync_playwright
from cryptography.fernet import Fernet
from sqlalchemy import create_engine, text, extract
from urllib.parse import quote
import pymysql
# 御龙测试环境
from Crypto.Cipher import AES
import binascii
from Crypto.Util.Padding import pad, unpad


# 全局变量,用于上报数据
record_id = ''  # Id:应用工作流唯一ID
server_url = 'https://yl.public.zj.cn/automation-workflow/api/' # url:服务端地址
flow_para = ''  # 参数
wf_id = '1873566777532829698'  # 流程部署ID
wf_name = '营收稽核结转报账'  # 流程名
rpa_code = 'revenue_audit'


class AESUtil:
    # 测试环境秘钥：16a41a967938d6428a89e5dca5350ae2
    # 正式环境秘钥：********************************
    aes_key = '********************************'

    def __init__(self, aes_key=''):
        if aes_key:
            self.aes_key = aes_key
        pass

    def encrypt(self, text):
        key = binascii.unhexlify(self.aes_key)
        cipher = AES.new(key, AES.MODE_ECB)  # 使用CBC模式，设置密钥和初始向量
        padded_text = pad(text.encode(), AES.block_size)  # 进行填充
        ciphertext = cipher.encrypt(padded_text)
        return binascii.hexlify(ciphertext).decode()

    def decrypt(self, encryptText):
        key = binascii.unhexlify(self.aes_key)
        cipher = AES.new(key, AES.MODE_ECB)
        ciphertext = binascii.unhexlify(encryptText)
        plaintext = cipher.decrypt(ciphertext)
        unpadded_plaintext = unpad(plaintext, AES.block_size)  # 去除填充
        return unpadded_plaintext.decode()

aes_util = AESUtil()
# 用于匹配数据库信息
robot_id = '2'
# 将pymysql注册为MySQL的适配器
pymysql.install_as_MySQLdb()



def get_sqlalchemy_engine():
    # 御龙测试库
    # host = '**************'
    # user = 'root'
    # password = 'Q/>l26yxzm5e'
    # database = 'pubinfo_digital'
    # port = '3306'
    # 御龙线上库，只能服务器连接
    host = '*************'
    user = 'pubinfowx'
    password = 'P5xdp#yt6'
    database = 'pubinfo_digital'
    port = '6606'
    encoded_password = quote(password)
    SQLALCHEMY_DATABASE_URI = f'mysql://{user}:{encoded_password}@{host}:{port}/{database}'
    engine = create_engine(SQLALCHEMY_DATABASE_URI, pool_size=20, max_overflow=50)
    return engine


def get_sql_result(sql, **kwargs):
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    num = 3
    while num > 0:
        try:
            with engine.connect() as connection:
                result = connection.execute(text(sql), parameters=kwargs)
                rows = result.fetchall()
            break
        except Exception as e:
            print(e)
            num -= 1
    return rows


def execute_sql(sql, **kwargs):
    sql = sql.replace(':', r'\:')
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    num = 3
    while num > 0:
        try:
            with engine.connect() as connection:
                connection.execute(text(sql), parameters=kwargs)
                connection.commit()
            break
        except Exception as e:
            print(e)
            num -= 1

    return True


def login_by_account(page, login, account, password):
    """
    账号密码登录
    """
    page.goto(login)
    page.wait_for_load_state()
    page.fill("#freename", account)
    page.fill("#freepassword", password)
    # 执行登录
    page.click('#login_btnDiv > input')
    page.wait_for_load_state()
    return page


def get_platform_account(name, type='5'):
    sql = f"SELECT a.`id`, a.`platform_login_name`, a.`platform_password` FROM pub_platform_account AS a JOIN pub_user AS b WHERE a.type='{type}' AND a.user_id=b.id AND b.real_name='{name}'"
    res = get_sql_result(sql)
    user_list = []
    if res:
        for row in res:
            nid, platform_login_name, encrypted_platform_password = row[0], row[1], row[2]
            decrypted_platform_password = aes_util.decrypt(encrypted_platform_password)
            user_list.append([platform_login_name, decrypted_platform_password, nid])
    user_list = [['wanghz.xc', 'xc*13414']]
    return user_list


def get_platform_account_from_rpa(code, type='5'):
    sql = f"SELECT a.`id`, a.`platform_login_name`, a.`platform_password` FROM pub_platform_account AS a JOIN pub_user AS b JOIN rpa_process_user AS c WHERE a.type='{type}' AND a.user_id=b.id AND b.id = c.user_id and c.rpa_info_code='{code}'"
    res = get_sql_result(sql)
    user_list = []
    if res:
        for row in res:
            nid, platform_login_name, encrypted_platform_password = row[0], row[1], row[2]
            decrypted_platform_password = aes_util.decrypt(encrypted_platform_password)
            user_list.append([platform_login_name, decrypted_platform_password, nid])
    return user_list


def update_is_password_right_2(nid):
    sql = f"update pub_platform_account set is_password_right=2 where id={nid}"
    execute_sql(sql)
    return True


@tasks_decorator
def run(playwright, path=None, id=None, url=None):
    if path:
        # 使用平台传入浏览器前缀 统一路径
        executable_path = path + r'\Lib\site-packages\ms-playwright\chromium-1060\chrome-win\chrome.exe'
    else:
        # 使用本机绝对路径
        # executable_path = r'C:\Program Files\HyperActuator\resources\Python39\Lib\site-packages\ms-playwright\chromium-1060\chrome-win\chrome.exe'
        executable_path = r'C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1148\chrome-win\chrome.exe'
        if not os.path.exists(executable_path):  # 判断释放存在文件
            logger.error(
                '请检查本机驱动路径是否存在，处理方案：桌面右键打开【助手/执行器】文件所在位置，替换executable_path参数的前缀后，重新运行！')
            return

    sql = f"SELECT count(*) FROM rpa_financial_supplier"
    supplier_num = get_sql_result(sql)
    if supplier_num[0][0] == 0:
        logger.error('请检查关联供应商清单是否已入库')
        return

    # sql = f"SELECT a.`platform_login_name`, a.`platform_password` FROM pub_platform_account AS a JOIN pub_user AS b WHERE a.type='5' AND a.user_id=b.id AND b.real_name='胡袁明'"
    # account_info = get_sql_result(sql)

    account_info = get_platform_account_from_rpa(rpa_code)
    logger.info(f'{len(account_info)}个用户启用了')

    for info in account_info:
        # 初始化浏览器对象 headless：False有头模式、True无头模式，executable_path浏览器路径
        # browser = playwright.firefox.launch(headless=False, executable_path=executable_path)
        browser = playwright.chromium.launch(headless=False, executable_path=executable_path)
        # 创建下上文环境，作用域：运行过程只要结束掉 则会关掉浏览器 释放内存
        context = browser.new_context()
        # 实例化页面对象 新建浏览器窗口页面
        page = context.new_page()
        # 跳转对应浏览器地址并登录
        page = login_by_account(page, "http://sso.zjtelecom.cn/cas/login", info[0], info[1])
        status = page.locator('//*[@id="status"]')
        print(status.is_visible())
        if status.is_visible():
            if '账号或密码错误' in status.inner_text() or '账户或密码已输入错误' in status.inner_text():
                update_is_password_right_2(info[2])
                logger.info(f'账号{info[0]}密码错误')
                continue

        time.sleep(2)
        # page1 = get_contract_info(page, '//img[contains(@src,"images/apps/app8.png")]')
        with page.expect_popup() as page1_info:
            page.get_by_text("营收资金稽核(新)").click()
        page1 = page1_info.value
        try:
            page1.wait_for_load_state(state="networkidle")
        except:
            pass
        page1.get_by_role("button", name="高级").click()
        page1.get_by_role("link", name="继续前往134.96.249.29（不安全）").click()
        try:
            page1.wait_for_load_state(state="networkidle")
        except:
            pass
        page1.get_by_role("menuitem", name="报账管理 ").locator("i").click()
        page1.get_by_role("link", name="营收结转报账").click()
        time.sleep(1)
        page1.locator("div").filter(has_text=re.compile(r"^查询日期$")).get_by_placeholder("选择日期").click()
        page1.get_by_role("button", name="天前").first.click()
        page1.locator("div").filter(has_text=re.compile(r"^~$")).get_by_placeholder("选择日期").click()
        page1.get_by_role("button", name="今天").last.click()
        text_select = [["营收结转-收入确认的应收挂台账", "el-table_8_column_26", "el-table_8_column_30", "el-table_8_column_29"],
                       ["营收结转-开票不确认收入的应收挂台账", "el-table_9_column_46", "el-table_9_column_50", "el-table_9_column_49"]]
        count_text = {"营收结转-收入确认的应收挂台账": 0, "营收结转-开票不确认收入的应收挂台账": 0}
        for text in text_select:
            # 收入确认的应收挂台账
            page1.get_by_role("textbox", name="请选择").first.click()
            page1.get_by_text(text[0]).click()
            page1.get_by_role("button", name=" 搜索").click()
            # try:
            #     page1.locator(
            #         ".el-pagination__sizes > .el-select > .el-input > .el-input__suffix > .el-input__suffix-inner > .el-select__caret").click()
            #     page1.get_by_text("100条/页").click()
            # except:
            #     pass
            deal_error = []
            while True:
                time.sleep(1)
                el_checkboxs_st = page1.locator(
                    ".el-table__fixed-body-wrapper > .el-table__body > tbody > .el-table__row").all()
                check_num = 0
                if len(el_checkboxs_st) == 0:
                    print('页面无数据，退出')
                    break
                page1.locator(
                    ".el-pagination__sizes > .el-select > .el-input > .el-input__suffix > .el-input__suffix-inner > .el-select__caret").click()
                page1.get_by_text("100条/页").click()
                page1.get_by_role("list").get_by_text("1", exact=True).click()
                # page1.get_by_role("button", name=" 搜索").click()
                time.sleep(1)

                status = True
                while status:
                    status = False
                    # el_checkboxs = page1.locator(
                    #     ".el-table__fixed-body-wrapper > .el-table__body > tbody > .el-table__row").all()
                    el_checkboxs = page1.locator(
                        ".el-table__fixed-body-wrapper > .el-table__body > tbody > .el-table__row").all()
                    for el_checkbox in el_checkboxs:
                        start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        approval_number_element = el_checkbox.locator(f".{text[3]}  > .cell.el-tooltip span")
                        approval_number = approval_number_element.text_content()
                        print(approval_number)
                        if approval_number in deal_error:
                            continue
                        # 获取付款人名称元素并打印其文本内容
                        payer_name_element = el_checkbox.locator(f".{text[2]}  > .cell.el-tooltip span")
                        payer_name = payer_name_element.text_content()
                        print(payer_name)
                        # 校验是否是内部供应商
                        sql = f"SELECT `name`,`code` FROM rpa_financial_supplier WHERE name='{payer_name}'"
                        supplier_info = get_sql_result(sql)
                        if len(supplier_info) == 0:
                            print(f'--{payer_name}不是内部关联供应商，执行勾选报账...')
                            # 获取复选框元素并勾选
                            checkbox_element = el_checkbox.locator(
                                f".{text[1]} > .cell > .el-checkbox > .el-checkbox__input > .el-checkbox__inner")
                            checkbox_element.check()
                            check_num += 1
                            # 一条一条提交
                            page1.get_by_role("button", name=" 报账").click()
                            page1.get_by_role("button", name=" 发起报账").click()
                            time.sleep(15)

                            try:
                                page1.get_by_role("button", name="关闭").click()
                                deal_error.append(payer_name)
                                continue
                            except:
                                pass

                            status = True
                            count_text[text[0]] += 1

                            biz_no = approval_number
                            user_name = info[0]
                            end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            rpa_util.data_report(biz_no, '1', start_time, end_time, wf_name, wf_id, server_url,
                                                 user_name, rpaCode=rpa_code)
                            break
                    # 自带刷新
                    # page1.get_by_role("button", name=" 搜索").click()

                # if check_num:
                #     page1.get_by_role("button", name=" 报账").click()
                #     page1.get_by_role("button", name=" 发起报账").click()
                #     time.sleep(5)

                # 判断是否有下一页
                try:
                    # 检查下一页开关
                    btn_next = page1.locator(
                        "#app > div > div.main-container > section > div > div.el-card.is-always-shadow > div > div:nth-child(2) > div.pagination-container > div > button.btn-next")
                    flag = btn_next.is_disabled()
                    if not flag:
                        btn_next.click()
                    else:
                        print('没有下一页了，退出')
                        break
                except Exception as e:
                    print(e)
                    break
            logger.info(f'{text[0]}共处理{count_text[text[0]]}条数据')
        time.sleep(2)
        try:
            context.close()
            browser.close()
        except:
            pass


if __name__ == '__main__':
    # 本地:获取当前文件夹 所在的本机绝对路径  平台:文件路径、浏览器路径、应用工作流唯一ID、服务端地址
    ley_list = sys.argv
    with sync_playwright() as playwright:
        if len(ley_list) > 1:
            path = ley_list[1]  # path:浏览器前缀统一路径
            id = ley_list[2]  # Id:应用工作流唯一ID
            url = ley_list[3]  # url:服务端地址
            run(playwright, path, id, url)
        else:
            # 本地执行
            run(playwright)
