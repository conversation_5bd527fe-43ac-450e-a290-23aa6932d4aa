# 用户表
CREATE TABLE IF NOT EXISTS rpa_users
(
    id           INT AUTO_INCREMENT PRIMARY KEY,
    created_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    username     VA<PERSON>HAR(255) NOT NULL,
    password     VARCHAR(255) NOT NULL COMMENT '对称加密',
    name         VARCHAR(255) NULL,
    phone_number VARCHAR(32) NULL COMMENT '手机号',
    status       ENUM ('Enable', 'Disable') DEFAULT 'Enable' NOT NULL,
    robot_id     VARCHAR(32) NULL COMMENT '机器人编号',
    -- 修改唯一约束，使其包含 username 和 robot_id 字段
    CONSTRAINT unique_username_robot_id UNIQUE (username, robot_id)
)
CHARSET = utf8
ROW_FORMAT = DYNAMIC;

# 已提交报账单号表
CREATE TABLE IF NOT EXISTS rpa.rpa_flow
(
    id          INT AUTO_INCREMENT PRIMARY KEY,
    flow_number VARCHAR(255) NOT NULL,
    created_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    robot_id    VARCHAR(32) NULL COMMENT '机器人编号',
    CONSTRAINT unique_flow_robot UNIQUE (flow_number, robot_id)
)
CHARSET = utf8
ROW_FORMAT = DYNAMIC;

# 财务系统关联平台供应商
CREATE TABLE IF NOT EXISTS rpa.rpa_financial_supplier
(
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) NOT NULL UNIQUE COMMENT '代码',
    name VARCHAR(100) NOT NULL COMMENT '名称'
#     relation_type_code VARCHAR(20) COMMENT '关联类型代码',
#     relation_type VARCHAR(50) COMMENT '关联类型',
#     partner_id INT COMMENT '贸易伙伴标识',
#     partner_name VARCHAR(100) COMMENT '贸易伙伴'
)
CHARSET = utf8
ROW_FORMAT = DYNAMIC;
