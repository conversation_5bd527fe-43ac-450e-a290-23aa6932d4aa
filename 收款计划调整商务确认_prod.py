# 收入收款计划变更
# 第三方模块，作用域：操作系统进行交互的模块
import os
# 第三方模块，作用域：传递部分参数
import sys
# 第三方模块，作用域：日期、时间
import time
# 第三方模块，作用域：表格处理
import pandas as pd
import re
import random
import shutil
import datetime

# 装饰器，作用域：执行状态更改，平台部分校验通过
from merakwebroombiz.aop.api_aop import tasks_decorator

# 日志组件：1.执行步骤
from merakwebroombiz.logger.output_logger import output_logger as logger
# 需要把1.33.0升级成1.49.0
from playwright.sync_api import sync_playwright
from cryptography.fernet import Fernet
from sqlalchemy import create_engine, text
from urllib.parse import quote
from openpyxl import load_workbook
import pymysql
# 御龙测试环境
from Crypto.Cipher import AES
import binascii
from Crypto.Util.Padding import pad, unpad
import rpa_util

# 全局变量,用于上报数据
record_id = ''  # Id:应用工作流唯一ID
server_url = 'https://yl.public.zj.cn/automation-workflow/api/' # url:服务端地址
flow_para = ''  # 参数
wf_id = '1879389168448147457'  # 流程部署ID
wf_name = '收款计划调整商务确认'  # 流程名
rpa_code = 'payment_plan_approve'


# 用于匹配数据库信息
robot_id = '5'
env = '待办'
lie = {'待办': '5', '已办': '3'}
sbtypes = {'待办': 's_sbtypes', '已办': 's_sbtypes_al'}

# 将pymysql注册为MySQL的适配器
pymysql.install_as_MySQLdb()


class AESUtil:
    # 测试环境秘钥：16a41a967938d6428a89e5dca5350ae2
    # 正式环境秘钥：********************************
    aes_key = '********************************'

    def __init__(self, aes_key=''):
        if aes_key:
            self.aes_key = aes_key
        pass

    def encrypt(self, text):
        key = binascii.unhexlify(self.aes_key)
        cipher = AES.new(key, AES.MODE_ECB)  # 使用CBC模式，设置密钥和初始向量
        padded_text = pad(text.encode(), AES.block_size)  # 进行填充
        ciphertext = cipher.encrypt(padded_text)
        return binascii.hexlify(ciphertext).decode()

    def decrypt(self, encryptText):
        key = binascii.unhexlify(self.aes_key)
        cipher = AES.new(key, AES.MODE_ECB)
        ciphertext = binascii.unhexlify(encryptText)
        plaintext = cipher.decrypt(ciphertext)
        unpadded_plaintext = unpad(plaintext, AES.block_size)  # 去除填充
        return unpadded_plaintext.decode()

aes_util = AESUtil()


def get_sqlalchemy_engine():
    # 御龙测试库
    # host = '**************'
    # user = 'root'
    # password = 'Q/>l26yxzm5e'
    # database = 'pubinfo_digital'
    # port = '3306'
    # 御龙线上库，只能服务器连接
    host = '*************'
    user = 'pubinfowx'
    password = 'P5xdp#yt6'
    database = 'pubinfo_digital'
    port = '6606'
    encoded_password = quote(password)
    SQLALCHEMY_DATABASE_URI = f'mysql://{user}:{encoded_password}@{host}:{port}/{database}'
    engine = create_engine(SQLALCHEMY_DATABASE_URI, pool_size=20, max_overflow=50)
    return engine


def get_sql_result(sql, **kwargs):
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    num = 3
    while num > 0:
        try:
            with engine.connect() as connection:
                result = connection.execute(text(sql), parameters=kwargs)
                rows = result.fetchall()
            break
        except Exception as e:
            print(e)
            num -= 1
    return rows


def execute_sql(sql, **kwargs):
    sql = sql.replace(':', r'\:')
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    num = 3
    while num > 0:
        try:
            with engine.connect() as connection:
                connection.execute(text(sql), parameters=kwargs)
                connection.commit()
            break
        except Exception as e:
            print(e)
            num -= 1

    return True


def get_platform_account_from_rpa(code, type='5'):
    sql = f"SELECT a.`id`, a.`platform_login_name`, a.`platform_password` FROM pub_platform_account AS a JOIN pub_user AS b JOIN rpa_process_user AS c WHERE a.type='{type}' AND (a.is_password_right is null or a.is_password_right=1) AND a.user_id=b.id AND b.id = c.user_id and c.rpa_info_code='{code}' and c.is_enable=1"
    res = get_sql_result(sql)
    user_list = []
    if res:
        for row in res:
            nid, platform_login_name, encrypted_platform_password = row[0], row[1], row[2]
            decrypted_platform_password = aes_util.decrypt(encrypted_platform_password)
            user_list.append([platform_login_name, decrypted_platform_password, nid])
    return user_list

def update_is_password_right_2(nid):
    sql = f"update pub_platform_account set is_password_right=2 where id={nid}"
    execute_sql(sql)
    return True


def login_by_account(page, login, account, password):
    """
    账号密码登录
    """
    page.goto(login)
    page.wait_for_load_state()
    page.fill("#freename", account)
    page.fill("#freepassword", password)
    # 执行登录
    page.click('#login_btnDiv > input')
    page.wait_for_load_state()
    return page


@tasks_decorator
def run(playwright, path=None, id=None, url=None):
    if path:
        # 使用平台传入浏览器前缀 统一路径
        executable_path = path + r'\Lib\site-packages\ms-playwright\chromium-1148\chrome-win\chrome.exe'
    else:
        # 使用本机绝对路径
        # executable_path = r'C:\Program Files\HyperActuator\resources\Python39\Lib\site-packages\ms-playwright\chromium-1060\chrome-win\chrome.exe'
        executable_path = r'C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1148\chrome-win\chrome.exe'
        if not os.path.exists(executable_path):  # 判断释放存在文件
            logger.error(
                '请检查本机驱动路径是否存在，处理方案：桌面右键打开【助手/执行器】文件所在位置，替换executable_path参数的前缀后，重新运行！')
            return

    # file_path = "D:\data\人员账号信息.xlsx"
    # account_info = load_account_info(file_path)
    account_info = get_platform_account_from_rpa(rpa_code)
    logger.info(f'{len(account_info)}个用户启用了')

    for info in account_info:
        # 初始化浏览器对象 headless：False有头模式、True无头模式，executable_path浏览器路径
        # browser = playwright.firefox.launch(headless=False, executable_path=executable_path)
        browser = playwright.chromium.launch(headless=False, executable_path=executable_path)
        # 创建下上文环境，作用域：运行过程只要结束掉 则会关掉浏览器 释放内存
        context = browser.new_context()
        # 实例化页面对象 新建浏览器窗口页面
        page = context.new_page()
        # 跳转对应浏览器地址并登录
        page = login_by_account(page, "http://sso.zjtelecom.cn/cas/login", info[0], info[1])

        status = page.locator('//*[@id="status"]')
        print(status.is_visible())
        if status.is_visible():
            if '账号或密码错误' in status.inner_text() or '账户或密码已输入错误' in status.inner_text():
                update_is_password_right_2(info[2])
                logger.info(f'账号{info[0]}密码错误')
                continue

        # eip_login(page, info["账号"], info["密码"])
        time.sleep(2)
        page.get_by_text("集团应用").click()
        try:
            with page.expect_popup() as page1_info:
                page.locator("#jt_apps div").filter(has_text="市场经营 集团ICT").get_by_role("img").first.click()
            page1 = page1_info.value
        except:
            continue
        try:
            page1.wait_for_load_state(state="networkidle", timeout=10000)
        except:
            pass

        # 关闭弹窗
        page1.on("dialog", lambda dialog: dialog.dismiss())

        time.sleep(1)
        page1.locator('//*[@id="contractLeft"]/div/ul/li/ul/li/a').filter(has_text=f"我的{env}").click()
        time.sleep(2)

        row_text = page1.locator("iframe[name=\"menuiframe\"]").content_frame.locator(
            '//*[@id="DataForm"]/tbody/tr[2]/td').first.inner_text()
        if row_text == '暂无记录!':
            print('页面无数据，退出')
            continue
        time.sleep(5)
        # 业务类型选择
        page1.locator("iframe[name=\"menuiframe\"]").content_frame.locator(
            f"input[name=\"ch_value\\({sbtypes[env]}\\)\"]").click()
        try:
            page1.locator("iframe[name=\"menuiframe\"]").content_frame.locator(
                f'//*[@id="ff_value({sbtypes[env]})"]/input[@chvalue="协议级收入收款计划调整"]').check()
        except:
            print('无协议级收入收款计划调整，跳过当前账号')
            continue
        # 当前办理步骤选择
        page1.locator("iframe[name=\"menuiframe\"]").content_frame.locator(
            "input[name=\"ch_value\\(s_currstatus\\)\"]").click()
        try:
            page1.locator("iframe[name=\"menuiframe\"]").content_frame.locator(
                '//*[@id="ff_value(s_currstatus)"]/input[@chvalue="申请人确认"]').check()
        except:
            print('未找到申请人确认步骤，跳过当前账号')
            continue
        page1.locator("iframe[name=\"menuiframe\"]").content_frame.get_by_role("cell", name="查询", exact=True).locator(
            "a").click()

        while True:
            time.sleep(1)
            iframe_locator = page1.locator("iframe[name=\"menuiframe\"]").content_frame
            # 待办第5列，已办第3列
            links = iframe_locator.locator(f'//*[@id="DataForm"]/tbody/tr/td[{lie[env]}]/a').all()
            for num, link in enumerate(links):
                start_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                approval_number = iframe_locator.locator(f'//*[@id="DataForm"]/tbody/tr[{num + 2}]/td[6]').inner_text()
                print(approval_number)

                title_text = link.inner_text()
                print(title_text)

                stage = iframe_locator.locator(f'//*[@id="DataForm"]/tbody/tr[{num+2}]/td[3]').inner_text()
                if stage not in ['申请人确认']:
                    print(f"当前环节{stage}不是商务确认阶段，跳过处理")
                    continue

                with page1.expect_popup() as page2_info:
                    link.click()
                page2 = page2_info.value
                page2.wait_for_load_state()
                time.sleep(2)

                title = page2.locator('body > form > div.WrapMiddle > div > h1').first.inner_text()
                print(title)
                if '（执行变更）' not in title:
                    print('不是执行变更，跳过处理')
                    page2.close()
                    continue

                # 提交
                print('准备提交')
                page2.locator("#btn1_sav_endrecover").click()
                time.sleep(2)
                page2.locator("iframe[name=\"ifrwindow\"]").content_frame.locator("#opinionlist").get_by_text("同意",
                                                                                                              exact=True).dblclick()

                page2.locator("iframe[name=\"ifrwindow\"]").content_frame.get_by_role("link",
                                                                                      name="确认").click()

                page2.close()
                # 数据上报
                biz_no = approval_number
                user_name = info[0]
                end_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                logger.info(f"{info[0]}:{title_text} 确认")
                rpa_util.data_report(biz_no, '1', start_time, end_time, wf_name, wf_id, server_url,
                                     user_name, rpaCode=rpa_code)

            try:
                flag = page1.locator("iframe[name=\"menuiframe\"]").content_frame.locator(
                    "#ajaxBottomBtndiv").get_by_role(
                    "link", name="下一页")
                if flag.is_visible():
                    flag.click()
                    page1.wait_for_load_state()
                else:
                    print('没有下一页了，退出')
                    break
            except Exception as e:
                print(e)
                break

        time.sleep(2)
        try:
            context.close()
            browser.close()
        except:
            pass


if __name__ == '__main__':
    # 本地:获取当前文件夹 所在的本机绝对路径  平台:文件路径、浏览器路径、应用工作流唯一ID、服务端地址
    ley_list = sys.argv
    with sync_playwright() as playwright:
        if len(ley_list) > 1:
            path = ley_list[1]  # path:浏览器前缀统一路径
            id = ley_list[2]  # Id:应用工作流唯一ID
            url = ley_list[3]  # url:服务端地址
            run(playwright, path, id, url)
        else:
            # 本地执行
            run(playwright)
