#!/usr/bin/env python
# -*- coding: utf-8 -*-

from datetime import datetime

from openpyxl import load_workbook,Workbook
import os
import shutil

from sheet1_income import income_statistics
from sheet3_cost import cost_statistics
from sheet2_income_cost import income_cost_statistics

basepath = os.getcwd()


# 将模板sheet页写入新的文件   pandas只能保存数据，不能保留格式
def create_new_excel(tmp_excel_path,new_excel_path):
    if not os.path.isfile(tmp_excel_path):
        print('模板文件不存在')
        return False
    shutil.copy2(tmp_excel_path, new_excel_path)
    return True


def sheet1_income(filepath, new_workbook):
    filename = '通信主业收入明细表(新方案）.xlsx'
    filename = os.path.join(filepath, filename)
    sheet_name = '收入-两期及月度波动'
    new_sheet_income = new_workbook[sheet_name]
    # 调用收入-两期及月度波动 统计方法
    income_statistics(new_sheet_income,filename)

def sheet2_income_cost(filepath, new_workbook):
    filenames = ['通信主业收入明细表(新方案）.xlsx','营业成本.xlsx']
    filenames = [os.path.join(filepath, i) for i in filenames]
    sheet_name = '收支匹配'
    new_sheet = new_workbook[sheet_name]
    # 调用收入-两期及月度波动 统计方法
    income_cost_statistics(new_sheet,filenames)


def sheet3_cost(filepath, new_workbook):
    filenames = ['管理费用.xlsx','销售费用.xlsx','研发费用.xlsx','营业成本.xlsx']
    filenames = [os.path.join(filepath, i) for i in filenames]
    sheet_name = '成本-两期及月度波动'
    new_sheet_income = new_workbook[sheet_name]
    # 调用收入-两期及月度波动 统计方法
    cost_statistics(new_sheet_income,filenames)


# 导出分析结果
def report_export(filepath):
    # 列出当前目录下的所有文件夹
    folders = [f for f in os.listdir(filepath) if os.path.isdir(os.path.join(filepath, f))]

    # 假设这里只有一个文件夹，选择第一个文件夹进入
    if folders:
        target_folder = folders[0]
        filepath = os.path.join(filepath, target_folder)

    tmp_excel_path = 'Template1.xlsx'
    # tmp_excel_path = os.path.join(basepath, tmp_excel_path)
    print(tmp_excel_path)
    new_excel_path = '数据结果分析.xlsx'
    new_excel_path = os.path.join(filepath, new_excel_path)
    # 将模板拷贝一份新的
    status = create_new_excel(tmp_excel_path, new_excel_path)
    if status is False:
        exit()

    files = os.listdir(filepath)
    for file in files:
        new_file = file.split('_')[-1]
        os.rename(os.path.join(filepath, file), os.path.join(filepath, new_file))

    # 打开新表
    new_workbook = load_workbook(new_excel_path)
    sheet1_income(filepath, new_workbook)
    sheet2_income_cost(filepath, new_workbook)
    sheet3_cost(filepath, new_workbook)

    new_workbook.save(new_excel_path)
