#!/usr/bin/env python
# -*- coding: utf-8 -*-
# from adodbapi.examples.xls_read import sheet
from openpyxl import load_workbook,Workbook

from openpyxl.utils import get_column_letter
from excel_style import excelstyle_base,excelstyle_merge,excelstyle_data,fill,fill_red

# 结合模板的编号列表，获取指定sheet页指定列的统计数据，并返回
def find_fields_date(index_title,title_to_find,sheet,cols_data):
    column_number = None
    column_data = None
    for col in sheet[1]:
        if col.value == index_title:
            # 找到标题，获取列号
            column_number = col.column -1
        if col.value == title_to_find:
            # 找到标题，获取列号  索引0开始 减一
            column_data = col.column - 1

    if column_number is None:
        print(f'未能找到名为‘{index_title}’的标题'+str(sheet))
        return
    if column_data is None:
        print(f'未能找到名为‘{title_to_find}’的标题')
        return

    sum_mon = 0
    for num in cols_data:
        for row in sheet.iter_rows(min_row=2, values_only=True):  # 从第2行开始获取数据
            # 匹配第一列的编号
            if str(row[column_number]) == num:
                # 计算编号总和
                # 遇到为空值的，当0跳过
                if row[column_data] is None:
                    continue
                sum_mon += row[column_data]
    return sum_mon

# 月度统计
def month_statistics(workbook, numlist, new_sheet,row,month_num, sheetnames, index_title='编号'):
    # sheetnames = workbook.sheetnames
    # 要查找的列
    title_to_yestd = '上年同期数'
    title_to_month = '本月发生数'
    title_to_now = '本年累计数'
    col = 0
    step = 2
    for sheetname in sheetnames:
        # if sheetname == '(页名映射表)':
        #     continue
        try:
            sheet = workbook[sheetname]
        except Exception as e:
            print(sheetname+'不存在')
            cell_5 = new_sheet.cell(row=row, column=col * step + 5)
            excelstyle_data(cell_5,'')
            cell_5.fill=fill_red
            cell_6 = new_sheet.cell(row=row, column=col * step + 6)
            excelstyle_data(cell_6, '')
            cell_6.fill = fill_red
            col += 1
            continue
        # 结合模板的编号列表，获取上年同期数
        money_yestd = find_fields_date(index_title, title_to_yestd, sheet, numlist)
        # print(money_yestd)
        # 获取本月发生数
        money_month = find_fields_date(index_title, title_to_month, sheet, numlist)
        # print(money_month)
        # 获取本年累计数
        money_now = find_fields_date(index_title, title_to_now, sheet, numlist)
        # print(money_now)

        # 若找不到标题返回None，继续下一个sheet
        if money_yestd is None or money_month is None or money_now is None:
            cell_5 = new_sheet.cell(row=row, column=col * step + 5)
            excelstyle_data(cell_5,'找不到标题')
            cell_5.fill=fill_red
            cell_6 = new_sheet.cell(row=row, column=col * step + 6)
            excelstyle_data(cell_6, '找不到标题')
            cell_6.fill = fill_red
            col += 1
            continue
        # 两期异常变动大于10%
        if money_yestd == 0:
            growth_rate = 0
        else:
            growth_rate = round((money_now - money_yestd) * 100 / money_yestd, 2)
        cell_5 = new_sheet.cell(row=row, column=col * step + 5)
        excelstyle_data(cell_5, str(growth_rate)+'%')
        if growth_rate > 10:
            cell_5.fill = fill
        # 单月成本为负或单月成本占比超过本期累计月均的50%，或低于本期累计月均的 30%
        cell_6 = new_sheet.cell(row=row, column=col * step + 6)
        excelstyle_data(cell_6, '')
        if money_month < 0:
            excelstyle_data(cell_6, money_month)
            cell_6.fill = fill
        else:
            if money_now == 0:
                growth_rate = 0
                excelstyle_data(cell_6, '')
            else:
                growth_rate = round(money_month * month_num / money_now, 2)
                if growth_rate >1.5:
                    excelstyle_data(cell_6, f'超过月均{round((growth_rate-1)*100,2)}%')
                    cell_6.fill = fill
                elif growth_rate <0.7:
                    excelstyle_data(cell_6, f'低于月均{round((1-growth_rate)*100,2)}%')
                    cell_6.fill = fill
                else:
                    excelstyle_data(cell_6, '')

        col += 1

def find_month_num(workbook):
    month_num = None
    sheetnames = workbook.sheetnames
    for sheetname in sheetnames:
        if '年' in sheetname and '月' in sheetname:
            month_num = int(sheetname.split('年')[1][:-1])
            break
    return month_num

# 创建表格标题,合并所有的sheet
def title(data_sheet_names, new_sheet):
    sheetnames = []
    for i in data_sheet_names:
        workbook = load_workbook(i)
        for sheetname in workbook.sheetnames:
            if sheetname == '(页名映射表)':
                continue
            if sheetname not in sheetnames:
                sheetnames.append(sheetname)
    # sheetnames = workbook.sheetnames
    col = 0
    step = 2
    for sheetname in sheetnames:
        if sheetname == '(页名映射表)':
            continue
        # 新增的单元格需要使用cell()方法，不能直接用数组形式新增 默认从1:1开始算
        cell_5 = new_sheet.cell(row=2, column=col * step + 5)
        excelstyle_base(cell_5, '两期异常变动大于10%')

        cell_6 = new_sheet.cell(row=2, column=col * step + 6)
        excelstyle_base(cell_6, '单月成本为负或单月成本占比超过本期累计月均的50%，或低于本期累计月均的 30%')

        try:
            province = sheetname.split()[1]
        except Exception as e:
            province = sheetname
        # 合并单元格
        new_sheet.merge_cells(start_row=1, start_column=col * step + 5, end_row=1, end_column=col * step + 6)
        cell_5_6 = new_sheet.cell(row=1, column=col * step + 5)
        excelstyle_merge(cell_5_6, province)

        col += 1
    # 给所有列设置列宽
    max_column = new_sheet.max_column
    for col in range(4, max_column + 1):
        new_sheet.column_dimensions[get_column_letter(col)].width = 20
    return sheetnames

def cost_statistics(new_sheet,data_sheet_names):
    min_row = 3
    sheet_dicts ={}
    # sheetnames = workbook.sheetnames
    # title(data_sheet_names,new_sheet)
    # 合并所有的sheet名称
    sheetnames = title(data_sheet_names, new_sheet)
    # 拿第一个表，创建标题
    workbook = load_workbook(data_sheet_names[0])
    month_num = find_month_num(workbook)
    if month_num is None:
        print('没有找到月份')
        return
    row_num = min_row
    for row in new_sheet.iter_rows(min_row=min_row, values_only=True):  # 从第3行开始获取数据
        # 读取第二列的表名
        filename = str(row[1])
        workbook = None
        if filename not in sheet_dicts.keys():
            for i in data_sheet_names:
                if filename in i:
                    workbook = load_workbook(i)
                    # 将已打开的excel加入列表，用于后续使用
                    sheet_dicts[filename] = workbook
                    break
        else:
            workbook = sheet_dicts[filename]
        # 若没有匹配到，excel名不存在
        if workbook is None:
            continue
        # 读取第三列的数据,获取编号列表
        # cols_data.append(str(row[2]).split('、'))
        print(filename + 'row:'+str(row_num))
        #编号
        numlist = str(row[2]).split('、')
        if filename =='研发费用':
            month_statistics(workbook, numlist, new_sheet, row_num, month_num,sheetnames,index_title='行次')
        else:
            month_statistics(workbook, numlist, new_sheet,row_num,month_num,sheetnames)
        row_num += 1

# 按装订区域中的绿色按钮以运行脚本。
if __name__ == '__main__':
    pass
