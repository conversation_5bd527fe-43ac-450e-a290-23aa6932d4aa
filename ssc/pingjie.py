code_all ="""
330000000000000
330000111111111
330000888888888
33000099999999C
330101429264741
330101429436100
330103142926474
330103712512215
3301047008021X0
330106474000130
330108142930625
33010814293062B
330108142944731
330109730915296
330165712535679
{code_first}
A122
A12201
A123
A202
A302
A3300
A3301
A3302
A3303
A3304
A3305
A3306
A3307
A3308
A3309
A3310
A3311
A3312
A3314
A3315
A3317
A3318
A3351
A3352
A368
A392
B011
B3300
B3301
B3302
B3303
B3304
B3305
B3306
B3307
B3308
B3309
B3310
B3311
B3312
H3300
H3301
H3302
H3303
H3304
H3305
H3306
H3307
H3308
H3309
H3310
H3311
H3312
H3313
H3314
H3315
W3920
W3921
W3922
W3923
W3924
W3925
W3926
W3927
W3928
W3929
W392A
W392B
"""

code_l ="""
A122
A12201
A123
A202
A302
A3300
A3301
A3302
A3303
A3304
A3305
A3306
A3307
A3308
A3309
A3310
A3311
A3312
A3314
A3315
A3317
A3318
A3351
A3352
A368
A392
H3300
H3301
H3302
H3303
H3304
H3305
H3306
H3307
H3308
H3309
H3310
H3311
H3312
H3313
H3314
H3315
W3920
W3921
W3922
W3923
W3924
W3925
W3926
W3927
W3928
W3929
W392A
W392B
"""

code_ll ="""
B3300
B3301
B3302
B3303
B3304
B3305
B3306
B3307
B3308
B3309
B3310
B3311
B3312
330000000000000
330000111111111
330000888888888
33000099999999C
330101429264741
330101429436100
330103142926474
330103712512215
3301047008021X0
330106474000130
330108142930625
33010814293062B
330108142944731
330109730915296
330165712535679
"""

def pingjie_income_cost(code_list, code_first='A011'):
    sql_1 = """
    select {code_first}.date_month AS date_month,
           {code_first}.month_num AS month_num,
           {code_first}.indicator_name        as indicator_name,
           {code_first}.sheet_name_a       as sheet_name_a,
           {code_first}.index_title_a        as index_title_a,
           {code_first}.product_name_a       as product_name_a,
           {code_first}.sheet_name_b        as sheet_name_b,
           {code_first}.index_title_b       as index_title_b,
           {code_first}.product_name_b       as product_name_b,
           {code_first}.increase_a       as increase_a_{code_first},
           {code_first}.increase_b  as increase_b_{code_first},
    """.format(code_first=code_first)

    sql_2 = ''
    a = """{code_name}.increase_a as increase_a_{code}, {code_name}.increase_b as increase_b_{code},
    """

    for code in code_list.split():
        if code:
            code = code.strip()
            if code[0].isdigit():
                code_name = 'N' + code
            else:
                code_name = code
            sql_2 = sql_2 + a.format(code_name=code_name, code=code)
    sql_2 = sql_2.rstrip().rstrip(',')

    sql_3 = """
    from invoice.bi_income_cost_custom as {code_first}
    """.format(code_first=code_first)

    sql_4 = ""
    b = """left join invoice.bi_income_cost_custom as {code_name} on {code_name}.code = '{code}' and {code_first}.indicator_name = {code_name}.indicator_name
    """
    for code in code_list.split():
        if code:
            code = code.strip()
            if code[0].isdigit():
                code_name = 'N' + code
            else:
                code_name = code
            sql_4 = sql_4 + b.format(code_name=code_name, code=code, code_first=code_first)

    sql_5 = """where {code_first}.code = '{code_first}'
    """.format(code_first=code_first)

    sql = sql_1 + sql_2 + sql_3 + sql_4 + sql_5
    return sql


def pingjie_income(code_list, code_first='A011'):
    sql_1 = """
    select {code_first}.date_month AS date_month,
           {code_first}.month_num AS month_num,
           {code_first}.index_title        as index_title,
           {code_first}.product_name       as product_name,

           {code_first}.period_yestd       as period_yestd_{code_first},
           {code_first}.occurrences_month  as occurrences_month_{code_first},
           {code_first}.cumulative_year    as cumulative_year_{code_first},
           {code_first}.increase           as increase_{code_first},
    """.format(code_first=code_first)

    sql_2 = ''
    a = """{code_name}.period_yestd       as period_yestd_{code},
       {code_name}.occurrences_month  as occurrences_month_{code},
       {code_name}.cumulative_year    as cumulative_year_{code},
       {code_name}.increase           as increase_{code},
    """

    for code in code_list.split():
        if code:
            code = code.strip()
            if code[0].isdigit():
                code_name = 'N' + code
            else:
                code_name = code
            sql_2 = sql_2 + a.format(code_name=code_name, code=code, code_first=code_first)
    sql_2 = sql_2.rstrip().rstrip(',')

    sql_3 = """
    from invoice.bi_income as {code_first}
    """.format(code_first=code_first)

    sql_4 = ""
    b = """left join invoice.bi_income as {code_name}
                   on {code_name}.code = '{code}' and {code_first}.index_title = {code_name}.index_title
    """
    for code in code_list.split():
        if code:
            code = code.strip()
            if code[0].isdigit():
                code_name = 'N' + code
            else:
                code_name = code
            sql_4 = sql_4 + b.format(code_name=code_name, code=code, code_first=code_first)

    sql_5 = """where {code_first}.code = '{code_first}'
    """.format(code_first=code_first)

    sql = sql_1 + sql_2 + sql_3 + sql_4 + sql_5
    return sql


if __name__ == '__main__':
    sql_income_a1 = pingjie_income(code_l)
    sql_income_a2 = pingjie_income(code_ll, 'B011')

    sql_custom_b1 = pingjie_income_cost(code_l)
    sql_custom_b2 = pingjie_income_cost(code_ll, 'B011')
    print('ok')