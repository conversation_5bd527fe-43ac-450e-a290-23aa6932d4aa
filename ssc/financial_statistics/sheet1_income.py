#!/usr/bin/env python
# -*- coding: utf-8 -*-

from openpyxl import load_workbook,Workbook

from openpyxl.utils import get_column_letter
from excel_style import excelstyle_base,excelstyle_merge,excelstyle_data,fill

# 结合模板的编号列表，获取指定sheet页指定列的统计数据，并返回
def find_fields_date(index_title,title_to_find,income_sheet,cols_data):
    money = []
    column_number = None
    column_data = None
    for col in income_sheet[1]:
        if col.value == index_title:
            # 找到标题，获取列号 索引0开始 减一
            column_number = col.column - 1
        if col.value == title_to_find:
            # 找到标题，获取列号  索引0开始 减一
            column_data = col.column - 1

    if column_number is None:
        print(f'未能找到名为‘{index_title}’的标题')
        return
    if column_data is None:
        print(f'未能找到名为‘{title_to_find}’的标题')
        return
    for i in cols_data:
        sum_mon = 0
        for num in i:
            for row in income_sheet.iter_rows(min_row=2, values_only=True):  # 从第2行开始获取数据
                # 匹配第一列的编号
                if str(row[column_number]) == num:
                    # 计算编号总和
                    # 遇到为空值的，当0跳过
                    if row[column_data] is None:
                        continue
                    sum_mon += row[column_data]
                    break
        money.append(sum_mon)
    return money

# 月度统计
def month_statistics(filename,cols_data,min_row,new_sheet):
    # 打开 通信主业收入明细表(新方案）.xlsx 填写10月份的数据
    income_workbook = load_workbook(filename)
    sheetnames = income_workbook.sheetnames
    # 要查找的列
    index_title = '编号'
    title_to_yestd = '上年同期数'
    title_to_month = '本月发生数'
    title_to_now = '本年累计数'
    col = 0

    for sheetname in sheetnames:
        if sheetname == '(页名映射表)':
            continue
        print(sheetname)
        income_sheet = income_workbook[sheetname]
        # 部分是统计金额 需要重新计算
        # title_growth_rate = '增幅（%）'
        # 结合模板的编号列表，获取上年同期数
        money_yestd = find_fields_date(index_title,title_to_yestd,income_sheet,cols_data)
        # print(money_yestd)
        # 获取本月发生数
        money_month = find_fields_date(index_title,title_to_month,income_sheet,cols_data)
        # print(money_month)
        # 获取本年累计数
        money_now = find_fields_date(index_title,title_to_now,income_sheet,cols_data)
        # print(money_now)
        #获取增幅（%）
        # total_money_rate = find_fields_date(index_title,title_growth_rate,income_sheet,cols_data)
        # print(total_money_rate)

        # 若找不到标题返回None，继续下一个sheet
        if money_yestd is None or money_month is None or money_now is None:
            continue
        # 获取编号行数，填写，默认从第三行开始
        len_write = len(cols_data)
        eq_money = 5 * 10 ** 7
        step = 4
        print(new_sheet.cell(row=2,column=col * step + 4))
        # 新增的单元格需要使用cell()方法，不能直接用数组形式新增 默认从1:1开始算
        cell_4 = new_sheet.cell(row=2,column=col * step + 4)
        excelstyle_base(cell_4, '本期收入金额大于5000万')

        cell_5 = new_sheet.cell(row=2,column=col * step + 5)
        excelstyle_base(cell_5, '两期波动幅度大于10%，或波动方向与股份合并相反')

        cell_6 = new_sheet.cell(row=2,column=col * step + 6)
        excelstyle_base(cell_6, '月收入占本期收入占比超过20%')

        cell_7 = new_sheet.cell(row=2,column=col * step + 7)
        excelstyle_base(cell_7, '月收入为负数')

        # print(new_sheet.merged_cells)
        # print(new_sheet[min_row - 1][col * 4 + 3])
        try:
            province = sheetname.split()[1]
        except Exception as e:
            province = sheetname
        # 合并单元格
        new_sheet.merge_cells(start_row=1,start_column=col * step + 4,end_row=1,end_column=col * step + 5)
        new_sheet.merge_cells(start_row=1, start_column=col * step + 6, end_row=1, end_column=col * step + 7)
        cell_4_5 = new_sheet.cell(row=1,column=col * step + 4)
        excelstyle_merge(cell_4_5, province +' 两期波动')
        cell_6_7 = new_sheet.cell(row=1,column=col * step + 6)
        excelstyle_merge(cell_6_7, province + ' 月度波动')

        for i in range(0,len_write):
            #两期波动1、本期收入金额大于5000万元，并且2、两期波动幅度大于10%，或波动方向与股份合并相反
            if money_yestd[i] == 0:
                growth_rate = 0
            else:
                growth_rate = round((money_now[i]-money_yestd[i])*100/money_yestd[i],2)
            cell_4 = new_sheet.cell(row=i + min_row, column=col * step + 4)
            excelstyle_data(cell_4,'否')
            cell_5 = new_sheet.cell(row=i + min_row, column=col * step + 5)
            excelstyle_data(cell_5, str(growth_rate)+'%')

            if money_now[i] > eq_money and abs(growth_rate)>10:
                cell_4.value = '是'
                cell_4.fill = fill
                cell_5.fill = fill

            #月度波动 1、某月收入占本期收入累计占比超过20%，或者；2、某月收入为负数
            if money_now[i] == 0:
                ratio = 0
            else:
                ratio = round(money_month[i]*100/money_now[i],2)
            cell_6 = new_sheet.cell(row=i + min_row, column=col * step + 6)
            excelstyle_data(cell_6, str(ratio)+'%')
            cell_7 = new_sheet.cell(row=i + min_row, column=col * step + 7)
            excelstyle_data(cell_7, '')

            if ratio > 20:
                cell_6.fill = fill
            if money_month[i] < 0:
                cell_7.value = money_month[i]
                cell_7.fill = fill

        col +=1
    # 给所有列设置列宽
    max_column = new_sheet.max_column
    for col in range(4, max_column + 1):
        print(get_column_letter(col))
        new_sheet.column_dimensions[get_column_letter(col)].width = 20

def income_statistics(new_sheet,data_sheet_name):
    cols_data = []
    min_row = 3
    for row in new_sheet.iter_rows(min_row=min_row, values_only=True):  # 从第3行开始获取数据
        # 读取第三列的数据,获取编号列表
        cols_data.append(str(row[2]).split('、'))
    print(cols_data)

    month_statistics(data_sheet_name, cols_data, min_row, new_sheet)

# 按装订区域中的绿色按钮以运行脚本。
if __name__ == '__main__':
    pass
