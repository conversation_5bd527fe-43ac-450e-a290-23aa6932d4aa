#!/usr/bin/env python
# -*- coding: utf-8 -*-

from datetime import datetime

from openpyxl import load_workbook,Workbook
import os
import shutil

from sheet1_income import income_statistics
from sheet3_cost import cost_statistics
from sheet2_income_cost import income_cost_statistics

basepath = os.getcwd()


# 将模板sheet页写入新的文件   pandas只能保存数据，不能保留格式
def create_new_excel(tmp_excel_path,new_excel_path):
    if not os.path.isfile(tmp_excel_path):
        print('模板文件不存在')
        return False
    shutil.copy2(tmp_excel_path, new_excel_path)
    return True


def sheet1_income(new_workbook):
    filename = 'file_excel/通信主业收入明细表(新方案）.xlsx'
    filename = os.path.join(basepath, filename)
    sheet_name = '收入-两期及月度波动'
    new_sheet_income = new_workbook[sheet_name]
    # 调用收入-两期及月度波动 统计方法
    income_statistics(new_sheet_income,filename)

def sheet2_income_cost(new_workbook):
    filenames = ['file_excel/通信主业收入明细表(新方案）.xlsx','file_excel/营业成本.xlsx']
    filenames = [os.path.join(basepath, i) for i in filenames]
    sheet_name = '收支匹配'
    new_sheet = new_workbook[sheet_name]
    # 调用收入-两期及月度波动 统计方法
    income_cost_statistics(new_sheet,filenames)


def sheet3_cost(new_workbook):
    filenames = ['file_excel/管理费用.xlsx','file_excel/销售费用.xlsx','file_excel/研发费用.xlsx','file_excel/营业成本.xlsx']
    filenames = [os.path.join(basepath, i) for i in filenames]
    sheet_name = '成本-两期及月度波动'
    new_sheet_income = new_workbook[sheet_name]
    # 调用收入-两期及月度波动 统计方法
    cost_statistics(new_sheet_income,filenames)

# 按装订区域中的绿色按钮以运行脚本。
if __name__ == '__main__':
    tmp_excel_path = 'Template1.xlsx'
    tmp_excel_path = os.path.join(basepath, tmp_excel_path)
    print(tmp_excel_path)
    new_excel_path = 'test.xlsx'
    new_excel_path = os.path.join(basepath, new_excel_path)
    # 将模板拷贝一份新的
    status = create_new_excel(tmp_excel_path,new_excel_path)
    if status is False:
        exit()
    # 打开新表
    new_workbook = load_workbook(new_excel_path)
    print('统计获取表1数据')
    sheet1_income(new_workbook)
    print('开始统计表2数据')
    sheet2_income_cost(new_workbook)
    print('开始统计表3数据')
    sheet3_cost(new_workbook)

    new_workbook.save(new_excel_path)
