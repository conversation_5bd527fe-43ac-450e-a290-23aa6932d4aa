#!/usr/bin/env python
# -*- coding: utf-8 -*-
# 在原表中插入多编号值


import os
from sqlalchemy import create_engine, text, extract
from urllib.parse import quote
import pymysql

# 将pymysql注册为MySQL的适配器
pymysql.install_as_MySQLdb()

business_map = {
    '通信主业收入明细表(新方案）': 'bi_income',
    '营业成本': 'bi_cost_business',
    '管理费用': 'bi_cost_manage',
    '销售费用': 'bi_cost_sale',
    '研发费用': 'bi_cost_research',
}

index_custom = [
    ['IDC收支匹配', '通信主业收入明细表(新方案）', '3.2.2.1.1、3.2.2.2.1', '主机托管收入两期变幅', '营业成本',
     '1.17.1.2.1', '营业成本-网元服务费两期变幅'],
    ['出售系统集成设备收支匹配', '通信主业收入明细表(新方案）', '4.1.1.8', '系统集成设备收入两期变幅', '营业成本',
     '1.20.1.3', '营业成本-出售系统集成设备两期变幅'],
    ['集成业务收支匹配', '通信主业收入明细表(新方案）', '3.1.3、3.1.4、3.2.4、3.2.5、3.3、3.4、3.5、3.6、3.7',
     '集成服务收入两期变幅', '营业成本', '1.17.1.4、1.17.1.5、1.17.2、1.17.3.2、1.17.3.3、1.17.4、1.17.5、1.17.6、1.17.7',
     '集成服务支出两期变幅'],
    ['天翼高清结算收支匹配', '通信主业收入明细表(新方案）',
     '2.3.5.2.2.1.1、2.3.5.2.2.2.1.1、2.3.5.2.2.2.2.1、2.3.5.2.3.1.1、2.3.5.2.3.2.1.1、2.3.5.2.3.2.2.1、2.3.5.2.4.1、2.3.5.2.5.1',
     '天翼高清收入两期变幅', '通信主业收入明细表(新方案）',
     '2.3.5.2.2.1.2、2.3.5.2.2.2.1.2、2.3.5.2.2.2.2.2、2.3.5.2.3.1.2、2.3.5.2.3.2.1.2、2.3.5.2.3.2.2.2、2.3.5.2.4.2、2.3.5.2.5.2',
     '天翼高清结算支出两期变幅'],
]

create_sql_templte = """
CREATE TABLE IF NOT EXISTS `bi_income_cost_custom` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `indicator_name` varchar(100) NOT NULL COMMENT '指标名称',
  `sheet_name_a` varchar(100) NOT NULL COMMENT '报表名称',
  `index_title_a` varchar(500) NOT NULL COMMENT '对应编号',
  `product_name_a` varchar(100) NOT NULL COMMENT '收入数据',
  `sheet_name_b` varchar(100) NOT NULL COMMENT '报表名称',
  `index_title_b` varchar(500) NOT NULL COMMENT '对应编号',
  `product_name_b` varchar(100) NOT NULL COMMENT '成本数据',
  `period_yestd_a` float NULL COMMENT '上年同期数',
  `occurrences_month_a` float NULL COMMENT '本月发生数',
  `cumulative_year_a` float NULL COMMENT '本年累计数',
  `increase_a` float NULL COMMENT '收入增幅',
  `period_yestd_b` float NULL COMMENT '上年同期数',
  `occurrences_month_b` float NULL COMMENT '本月发生数',
  `cumulative_year_b` float NULL COMMENT '本年累计数',
  `increase_b` float NULL COMMENT '成本增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`indicator_name`, `code`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
"""


def get_sqlalchemy_engine():
    host = '*************'
    user = 'pjadmin'
    password = 'Rt6#9Tae1'
    database = 'invoice'
    port = '8921'
    encoded_password = quote(password)
    SQLALCHEMY_DATABASE_URI = f'mysql://{user}:{encoded_password}@{host}:{port}/{database}'
    engine = create_engine(SQLALCHEMY_DATABASE_URI, pool_size=20, max_overflow=50)
    return engine


def get_sql_result(sql, **kwargs):
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    num = 3
    while num > 0:
        try:
            with engine.connect() as connection:
                result = connection.execute(text(sql), parameters=kwargs)
                rows = result.fetchall()
            break
        except Exception as e:
            print(e)
            num -= 1
    return rows


def execute_sql(sql, **kwargs):
    sql = sql.replace(':', r'\:')
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    num = 3
    while num > 0:
        try:
            with engine.connect() as connection:
                connection.execute(text(sql), parameters=kwargs)
                connection.commit()
            break
        except Exception as e:
            print(e)
            num -= 1

    return True


def create_income_cost(month):
    cols_data = []
    for line in index_custom:
        indicator_name, sheet_name_a, index_title_a, product_name_a, sheet_name_b, index_title_b, product_name_b = line[
                                                                                                                   :7]
        table_name = business_map[sheet_name_a]
        index_title_list_a = index_title_a.split('、')
        if len(index_title_list_a) == 1:
            index_query_a = f"='{index_title_list_a[0]}'"
        else:
            index_query_a = f' in {tuple(index_title_list_a)}'
        sql_query = f"select SUM(period_yestd) as `period_yestd_a`, SUM(occurrences_month) as `occurrences_month_a`, SUM(cumulative_year) as `cumulative_year_a`, `code`, `company`, `date_month` from {table_name} where date_month='{month}' and index_title{index_query_a} GROUP BY `code`,`date_month`"
        rows = get_sql_result(sql_query)
        for row in rows:
            increase_b = 0
            period_yestd_a, occurrences_month_a, cumulative_year_a, code, company, date_month = row[:]
            if period_yestd_a is None or period_yestd_a == 0 or cumulative_year_a is None or cumulative_year_a == 0:
                increase_a = 0
            else:
                increase_a = cumulative_year_a / period_yestd_a - 1
            # 再根据code获取成本数据
            index_title_list_b = index_title_b.split('、')
            if len(index_title_list_b) == 1:
                index_query_b = f"='{index_title_list_b[0]}'"
            else:
                index_query_b = f' in {tuple(index_title_list_b)}'
            sql_query = f"select SUM(period_yestd) as `period_yestd_b`, SUM(occurrences_month) as `occurrences_month_b`, SUM(cumulative_year) as `cumulative_year_b` from {table_name} where date_month='{date_month}' and index_title{index_query_b} and code='{code}'"
            rows_2 = get_sql_result(sql_query)
            if rows_2:
                period_yestd_b, occurrences_month_b, cumulative_year_b = rows_2[0]
                if period_yestd_b is None or period_yestd_b == 0 or cumulative_year_b is None or cumulative_year_b == 0:
                    increase_b = 0
                else:
                    increase_b = cumulative_year_b / period_yestd_b - 1
            else:
                period_yestd_b, occurrences_month_b, cumulative_year_b, increase_b = 0, 0, 0, 0
            line_data = line + [period_yestd_a, occurrences_month_a, cumulative_year_a, increase_a, period_yestd_b,
                                occurrences_month_b, cumulative_year_b, increase_b, code, company, date_month]
            cols_data.append(f"{tuple(line_data)}")
    sql_custom = "insert into bi_income_cost_custom (indicator_name, sheet_name_a, index_title_a, product_name_a, sheet_name_b, index_title_b, product_name_b, period_yestd_a, occurrences_month_a, cumulative_year_a, increase_a, period_yestd_b, occurrences_month_b, cumulative_year_b, increase_b, code, company, date_month) values %s" % (
    ','.join(cols_data),)
    sql_custom = sql_custom.replace('None', 'null')
    # execute_sql(sql_custom)
    print(f"收支匹配-{month}处理完毕")


if __name__ == '__main__':
    create_income_cost('2024-12')
