#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
from openpyxl import load_workbook,Workbook
from sqlalchemy import create_engine, text, extract
from urllib.parse import quote
import pymysql
# 将pymysql注册为MySQL的适配器
pymysql.install_as_MySQLdb()

create_sql_templte = """
CREATE TABLE IF NOT EXISTS `bi_income_{code}` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(500) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `code`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
"""


def get_sqlalchemy_engine():
    host = '*************'
    user = 'pjadmin'
    password = 'Rt6#9Tae1'
    database = 'invoice'
    port = '8921'
    encoded_password = quote(password)
    SQLALCHEMY_DATABASE_URI = f'mysql://{user}:{encoded_password}@{host}:{port}/{database}'
    engine = create_engine(SQLALCHEMY_DATABASE_URI, pool_size=20, max_overflow=50)
    return engine


def get_sql_result(sql, **kwargs):
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    num = 3
    while num > 0:
        try:
            with engine.connect() as connection:
                result = connection.execute(text(sql), parameters=kwargs)
                rows = result.fetchall()
            break
        except Exception as e:
            print(e)
            num -= 1
    return rows


def execute_sql(sql, **kwargs):
    sql = sql.replace(':', r'\:')
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    num = 3
    while num > 0:
        try:
            with engine.connect() as connection:
                connection.execute(text(sql), parameters=kwargs)
                connection.commit()
            break
        except Exception as e:
            print(e)
            num -= 1

    return True


def insert_db(filepath, month):
    # 列出当前目录下的所有文件夹
    folders = [f for f in os.listdir(filepath) if os.path.isdir(os.path.join(filepath, f))]

    # 假设这里只有一个文件夹，选择第一个文件夹进入
    if folders:
        target_folder = folders[0]
        filepath = os.path.join(filepath, target_folder)
    new_excel_path = '通信主业收入明细表(新方案）.xlsx'
    new_excel_path = os.path.join(filepath, new_excel_path)
    data_statistics(new_excel_path, month)


def data_statistics(filename, month):
    sql_create_all = ""
    sql_insert_all = ""
    sql_code_insert_all = ""
    sql_drop_all = ""
    # 打开 通信主业收入明细表(新方案）.xlsx 填写10月份的数据
    income_workbook = load_workbook(filename)
    sheetnames = income_workbook.sheetnames
    for sheetname in sheetnames:
        if sheetname == '(页名映射表)':
            continue
        print(sheetname)
        # 时间有时候不对
        code_company = sheetname.split('_')
        code = code_company[0].strip()
        company = code_company[1].strip()
        create_sql = create_sql_templte.format(code=code)
        print(create_sql)
        sql_create_all += create_sql + "\n"
        sql_drop_all = f"drop table if exists `bi_income_{code}`;\n" + sql_drop_all
        # continue
        # execute_sql(create_sql)
        # query_sql = "select * from `bi_income_%s` where date_month='%s';" % (code, month)
        # rows = get_sql_result(query_sql)
        # if len(rows) != 0:
        #     continue
        income_sheet = income_workbook[sheetname]
        cols_data = []
        min_row = 3
        for row in income_sheet.iter_rows(min_row=min_row, values_only=True):  # 从第3行开始获取数据
            # 读取第三列的数据,获取编号列表
            if row[0] and row[1]:
                index_title = row[0].strip()
                product_name = row[1].strip() if row[1] else ''
                title_to_yestd = row[3]
                title_to_month = row[4]
                title_to_now = row[5]
                title_increase = row[6]
                print(index_title, product_name, title_to_yestd, title_to_month, title_to_now, title_increase)
                cols_data.append(f"('{index_title}', '{product_name}', {title_to_yestd}, {title_to_month}, {title_to_now}, {title_increase}, '{code}', '{company}', '{month}')")

        sql = "insert into `bi_income_%s` (`index_title`, `product_name`, `period_yestd`, `occurrences_month`, `cumulative_year`, `increase`, `code`, `company`, `date_month`) values %s;" % (code, ','.join(cols_data))
        sql = sql.replace('None', 'null')
        sql_code = "insert into `bi_income` (`index_title`, `product_name`, `period_yestd`, `occurrences_month`, `cumulative_year`, `increase`, `code`, `company`, `date_month`) values %s;" % (','.join(cols_data), )
        sql_code = sql_code.replace('None', 'null')
        # print(sql)
        # execute_sql(sql)
        sql_code_insert_all += sql_code + "\n"
        sql_insert_all += sql + "\n"
    bi_income_communication_sql = create_sql_templte + "\n" + sql_code_insert_all
    print(f"{filename}-{month}处理完毕")

if __name__ == '__main__':
    insert_db('/data/财务月报_20250103165923', '2024-12')