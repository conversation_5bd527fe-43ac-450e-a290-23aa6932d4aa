#!/usr/bin/env python
# -*- coding: utf-8 -*-

from openpyxl import load_workbook,Workbook

from openpyxl.utils import get_column_letter
from excel_style import excelstyle_base,excelstyle_merge,excelstyle_data,fill,fill_red

# 结合模板的编号列表，获取指定sheet页指定列的统计数据，并返回
def find_fields_date(index_title,title_to_find,income_sheet,cols_data):
    column_number = None
    column_data = None
    for col in income_sheet[1]:
        if col.value == index_title:
            # 找到标题，获取列号
            column_number = col.column -1
        if col.value == title_to_find:
            # 找到标题，获取列号  索引0开始 减一
            column_data = col.column - 1

    if column_number is None:
        print(f'未能找到名为‘{index_title}’的标题')
        return
    if column_data is None:
        print(f'未能找到名为‘{title_to_find}’的标题')
        return

    sum_mon = 0
    for num in cols_data:
        for row in income_sheet.iter_rows(min_row=2, values_only=True):  # 从第2行开始获取数据
            # 匹配第一列的编号
            if str(row[column_number]) == num:
                # 计算编号总和
                # 遇到为空值的，当0跳过
                if row[column_data] is None:
                    continue
                sum_mon += row[column_data]
    return sum_mon

# 月度统计
def month_statistics(workbook, numlist,new_sheet,row,sheetnames,base_col):
    # sheetnames = workbook.sheetnames
    # 要查找的列
    index_title = '编号'
    title_to_yestd = '上年同期数'
    title_to_now = '本年累计数'
    col = 0
    step = 4
    for sheetname in sheetnames:
        # if sheetname == '(页名映射表)':
        #     continue
        try:
            sheet = workbook[sheetname]
        except Exception as e:
            print(sheetname+'不存在')
            newcell = new_sheet.cell(row=row, column=col * step + base_col)
            excelstyle_data(newcell, '找不到sheet页')
            newcell.fill=fill_red
            col += 1
            continue
        # 计算收入
        # sheet = workbook[sheetname]
        # 结合模板的编号列表，获取上年同期数
        money_yestd = find_fields_date(index_title, title_to_yestd, sheet, numlist)
        # 获取本年累计数
        money_now = find_fields_date(index_title, title_to_now, sheet, numlist)
        # 若找不到标题返回None，继续下一个sheet
        if money_yestd is None or money_now is None:
            newcell = new_sheet.cell(row=row, column=col * step + base_col)
            excelstyle_data(newcell,'找不到标题')
            newcell.fill=fill_red
            col += 1
            continue
        # 两期增幅
        if money_yestd == 0:
            growth_rate = 0
        else:
            growth_rate = round((money_now - money_yestd) * 100 / money_yestd, 2)
        newcell = new_sheet.cell(row=row, column=col * step + base_col)
        excelstyle_data(newcell, str(growth_rate)+'%')

        col += 1

#收入增幅与成本增幅差异大于20%  收入与成本变动幅度相反
def compare_rate(new_sheet,row,sheet_num):
    step = 4
    for col in range(0,sheet_num):
        cell_8 = new_sheet.cell(row=row, column=col * step + 8)
        income_rate = cell_8.value
        cell_9 = new_sheet.cell(row=row, column=col * step + 9)
        cost_rate = cell_9.value
        if income_rate is None or cost_rate is None:
            cell_10 = new_sheet.cell(row=row, column=col * step + 10)
            excelstyle_data(cell_10, '')
            cell_10.fill = fill_red
            cell_11 = new_sheet.cell(row=row, column=col * step + 11)
            excelstyle_data(cell_11, '')
            cell_11.fill = fill_red
            continue

        cell_10 = new_sheet.cell(row=row, column=col * step + 10)
        excelstyle_data(cell_10, '')
        cell_11 = new_sheet.cell(row=row, column=col * step + 11)
        excelstyle_data(cell_11, '')

        try:
            income_rate = float(income_rate.replace('%', ''))
            cost_rate = float(cost_rate.replace('%', ''))
        except Exception as e:
            cell_10.fill = fill_red
            cell_11.fill = fill_red
            continue

        if abs(income_rate - cost_rate) > 20:
            # excelstyle_data(cell_10, '是')
            cell_10.value = '是'
            cell_10.fill = fill
        elif (income_rate > 0 and cost_rate < 0) or (income_rate < 0 and cost_rate > 0):
            # excelstyle_data(cell_11, '是')
            cell_11.value = '是'
            cell_11.fill = fill

# 创建表格标题
def title(data_sheet_names, new_sheet):
    sheetnames = []
    for i in data_sheet_names:
        workbook = load_workbook(i)
        for sheetname in workbook.sheetnames:
            if sheetname == '(页名映射表)':
                continue
            if sheetname not in sheetnames:
                sheetnames.append(sheetname)
    col = 0
    for sheetname in sheetnames:
        step = 4
        # 新增的单元格需要使用cell()方法，不能直接用数组形式新增 默认从1:1开始算
        cell_8 = new_sheet.cell(row=2, column=col * step + 8)
        excelstyle_base(cell_8, '收入增幅')

        cell_9 = new_sheet.cell(row=2, column=col * step + 9)
        excelstyle_base(cell_9, '成本增幅')

        cell_10 = new_sheet.cell(row=2, column=col * step + 10)
        excelstyle_base(cell_10, '收入增幅与成本增幅差异大于20%')

        cell_11 = new_sheet.cell(row=2, column=col * step + 11)
        excelstyle_base(cell_11, '收入与成本变动幅度相反')

        try:
            province = sheetname.split()[1]
        except Exception as e:
            province = sheetname
        # 合并单元格
        new_sheet.merge_cells(start_row=1, start_column=col * step + 8, end_row=1, end_column=col * step + 11)
        cell_8_11 = new_sheet.cell(row=1, column=col * step + 8)
        excelstyle_merge(cell_8_11, province)
        print(col)
        print(cell_8_11)
        col += 1
    # 给所有列设置列宽
    max_column = new_sheet.max_column
    for i in range(4, max_column + 1):
        print(get_column_letter(i))
        new_sheet.column_dimensions[get_column_letter(i)].width = 20
    return col, sheetnames

def workbook_get(row,sheet_dicts,data_sheet_names,cols):
    filename = str(row[cols])
    workbook = None
    if filename not in sheet_dicts.keys():
        for i in data_sheet_names:
            if filename in i:
                workbook = load_workbook(i)
                # 将已打开的excel加入列表，用于后续使用
                sheet_dicts[filename] = workbook
                break
    else:
        workbook = sheet_dicts[filename]
    return workbook

def income_cost_statistics(new_sheet,data_sheet_names):
    min_row = 3
    sheet_dicts ={}
    # 拿第一个表，创建标题
    # workbook = load_workbook(data_sheet_names[0])
    # 统一使用第一个excel的sheet，防止错乱
    # sheetnames = workbook.sheetnames
    # sheet_num = title(workbook, new_sheet)
    # 合并所有的sheet名称
    sheet_num,sheetnames = title(data_sheet_names, new_sheet)
    row_num = min_row
    for row in new_sheet.iter_rows(min_row=min_row, values_only=True):  # 从第3行开始获取数据
        # 读取第二列的表名,获取workbook
        workbook_income = workbook_get(row,sheet_dicts,data_sheet_names,1)
        # 若没有匹配到，excel名不存在
        if workbook_income is None:
            continue
        #读取第三列的数据,获取编号列表
        numlist_income = str(row[2]).split('、')
        month_statistics(workbook_income, numlist_income, new_sheet, row_num, sheetnames, base_col=8)

        # 读取第5列的表名,获取workbook
        workbook_cost = workbook_get(row,sheet_dicts,data_sheet_names,4)
        # 若没有匹配到，excel名不存在
        if workbook_cost is None:
            continue
        # 读取第6列的数据,获取编号列表
        numlist_cost = str(row[5]).split('、')
        month_statistics(workbook_cost, numlist_cost, new_sheet, row_num, sheetnames, base_col=9)
        compare_rate(new_sheet,row_num,sheet_num)
        row_num += 1



# 按装订区域中的绿色按钮以运行脚本。
if __name__ == '__main__':
    pass
