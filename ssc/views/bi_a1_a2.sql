SELECT DISTINCT
  *
FROM
  `bi_income_cost_view_1`
  LEFT JOIN `bi_income_cost_view_2` USING (
      date_month,
      month_num,
      indicator_name,
      sheet_name_a,
      index_title_a,
      product_name_a,
      sheet_name_b,
      index_title_b,
      product_name_b
    );


SELECT DISTINCT
  *
FROM
  `bi_income_view_1`
  LEFT JOIN `bi_income_view_2` USING (
      date_month,
      month_num,
      index_title,
      product_name
    );
