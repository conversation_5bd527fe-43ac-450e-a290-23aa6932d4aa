SELECT
 `A202`.`date_month` AS `date_month`,
 `A202`.`month_num` AS `month_num`,
  A202.indicator_name AS indicator_name,
  A202.sheet_name_a AS sheet_name_a,
  A202.index_title_a AS index_title_a,
  A202.product_name_a AS product_name_a,
  A202.sheet_name_b AS sheet_name_b,
  A202.index_title_b AS index_title_b,
  A202.product_name_b AS product_name_b,
  A202.increase_a AS increase_a_A202,
  A202.increase_b AS increase_b_A202,
  N330000000000000.increase_a AS increase_a_330000000000000,
  N330000000000000.increase_b AS increase_b_330000000000000,
  N330000111111111.increase_a AS increase_a_330000111111111,
  N330000111111111.increase_b AS increase_b_330000111111111,
  N330000888888888.increase_a AS increase_a_330000888888888,
  N330000888888888.increase_b AS increase_b_330000888888888,
  N33000099999999C.increase_a AS increase_a_33000099999999C,
  N33000099999999C.increase_b AS increase_b_33000099999999C,
  N330108142944731.increase_a AS increase_a_330108142944731,
  N330108142944731.increase_b AS increase_b_330108142944731,
  A011.increase_a AS increase_a_A011,
  A011.increase_b AS increase_b_A011,
  A122.increase_a AS increase_a_A122,
  A122.increase_b AS increase_b_A122,
  A12201.increase_a AS increase_a_A12201,
  A12201.increase_b AS increase_b_A12201,
  A123.increase_a AS increase_a_A123,
  A123.increase_b AS increase_b_A123,
  A302.increase_a AS increase_a_A302,
  A302.increase_b AS increase_b_A302,
  A3300.increase_a AS increase_a_A3300,
  A3300.increase_b AS increase_b_A3300,
  A3301.increase_a AS increase_a_A3301,
  A3301.increase_b AS increase_b_A3301,
  A3302.increase_a AS increase_a_A3302,
  A3302.increase_b AS increase_b_A3302,
  A3303.increase_a AS increase_a_A3303,
  A3303.increase_b AS increase_b_A3303,
  A3304.increase_a AS increase_a_A3304,
  A3304.increase_b AS increase_b_A3304,
  A3305.increase_a AS increase_a_A3305,
  A3305.increase_b AS increase_b_A3305,
  A3306.increase_a AS increase_a_A3306,
  A3306.increase_b AS increase_b_A3306,
  A3307.increase_a AS increase_a_A3307,
  A3307.increase_b AS increase_b_A3307,
  A3308.increase_a AS increase_a_A3308,
  A3308.increase_b AS increase_b_A3308,
  A3309.increase_a AS increase_a_A3309,
  A3309.increase_b AS increase_b_A3309,
  A3310.increase_a AS increase_a_A3310,
  A3310.increase_b AS increase_b_A3310,
  A3311.increase_a AS increase_a_A3311,
  A3311.increase_b AS increase_b_A3311,
  A3312.increase_a AS increase_a_A3312,
  A3312.increase_b AS increase_b_A3312,
  A3314.increase_a AS increase_a_A3314,
  A3314.increase_b AS increase_b_A3314,
  A3315.increase_a AS increase_a_A3315,
  A3315.increase_b AS increase_b_A3315,
  A3317.increase_a AS increase_a_A3317,
  A3317.increase_b AS increase_b_A3317,
  A3318.increase_a AS increase_a_A3318,
  A3318.increase_b AS increase_b_A3318,
  A3351.increase_a AS increase_a_A3351,
  A3351.increase_b AS increase_b_A3351,
  A3352.increase_a AS increase_a_A3352,
  A3352.increase_b AS increase_b_A3352,
  A368.increase_a AS increase_a_A368,
  A368.increase_b AS increase_b_A368,
  A392.increase_a AS increase_a_A392,
  A392.increase_b AS increase_b_A392,
  B011.increase_a AS increase_a_B011,
  B011.increase_b AS increase_b_B011,
  B3300.increase_a AS increase_a_B3300,
  B3300.increase_b AS increase_b_B3300,
  B3301.increase_a AS increase_a_B3301,
  B3301.increase_b AS increase_b_B3301,
  B3302.increase_a AS increase_a_B3302,
  B3302.increase_b AS increase_b_B3302,
  B3303.increase_a AS increase_a_B3303,
  B3303.increase_b AS increase_b_B3303,
  B3304.increase_a AS increase_a_B3304,
  B3304.increase_b AS increase_b_B3304,
  B3305.increase_a AS increase_a_B3305,
  B3305.increase_b AS increase_b_B3305,
  B3306.increase_a AS increase_a_B3306,
  B3306.increase_b AS increase_b_B3306,
  B3307.increase_a AS increase_a_B3307,
  B3307.increase_b AS increase_b_B3307,
  B3308.increase_a AS increase_a_B3308,
  B3308.increase_b AS increase_b_B3308,
  B3309.increase_a AS increase_a_B3309,
  B3309.increase_b AS increase_b_B3309,
  B3310.increase_a AS increase_a_B3310,
  B3310.increase_b AS increase_b_B3310,
  B3311.increase_a AS increase_a_B3311,
  B3311.increase_b AS increase_b_B3311,
  B3312.increase_a AS increase_a_B3312,
  B3312.increase_b AS increase_b_B3312,
  H3300.increase_a AS increase_a_H3300,
  H3300.increase_b AS increase_b_H3300,
  H3301.increase_a AS increase_a_H3301,
  H3301.increase_b AS increase_b_H3301,
  H3302.increase_a AS increase_a_H3302,
  H3302.increase_b AS increase_b_H3302,
  H3303.increase_a AS increase_a_H3303,
  H3303.increase_b AS increase_b_H3303,
  H3304.increase_a AS increase_a_H3304,
  H3304.increase_b AS increase_b_H3304,
  H3305.increase_a AS increase_a_H3305,
  H3305.increase_b AS increase_b_H3305,
  H3306.increase_a AS increase_a_H3306,
  H3306.increase_b AS increase_b_H3306,
  H3307.increase_a AS increase_a_H3307,
  H3307.increase_b AS increase_b_H3307,
  H3308.increase_a AS increase_a_H3308,
  H3308.increase_b AS increase_b_H3308,
  H3309.increase_a AS increase_a_H3309,
  H3309.increase_b AS increase_b_H3309,
  H3310.increase_a AS increase_a_H3310,
  H3310.increase_b AS increase_b_H3310,
  H3311.increase_a AS increase_a_H3311,
  H3311.increase_b AS increase_b_H3311,
  H3312.increase_a AS increase_a_H3312,
  H3312.increase_b AS increase_b_H3312,
  H3313.increase_a AS increase_a_H3313,
  H3313.increase_b AS increase_b_H3313,
  H3314.increase_a AS increase_a_H3314,
  H3314.increase_b AS increase_b_H3314,
  H3315.increase_a AS increase_a_H3315,
  H3315.increase_b AS increase_b_H3315,
  W3920.increase_a AS increase_a_W3920,
  W3920.increase_b AS increase_b_W3920,
  W3921.increase_a AS increase_a_W3921,
  W3921.increase_b AS increase_b_W3921,
  W3922.increase_a AS increase_a_W3922,
  W3922.increase_b AS increase_b_W3922,
  W3923.increase_a AS increase_a_W3923,
  W3923.increase_b AS increase_b_W3923,
  W3924.increase_a AS increase_a_W3924,
  W3924.increase_b AS increase_b_W3924,
  W3925.increase_a AS increase_a_W3925,
  W3925.increase_b AS increase_b_W3925,
  W3926.increase_a AS increase_a_W3926,
  W3926.increase_b AS increase_b_W3926,
  W3927.increase_a AS increase_a_W3927,
  W3927.increase_b AS increase_b_W3927,
  W3928.increase_a AS increase_a_W3928,
  W3928.increase_b AS increase_b_W3928,
  W3929.increase_a AS increase_a_W3929,
  W3929.increase_b AS increase_b_W3929,
  W392A.increase_a AS increase_a_W392A,
  W392A.increase_b AS increase_b_W392A,
  W392B.increase_a AS increase_a_W392B,
  W392B.increase_b AS increase_b_W392B
FROM
  invoice.bi_income_cost_custom AS A202
  LEFT JOIN invoice.bi_income_cost_custom AS N330000000000000
    ON N330000000000000.code = '330000000000000'
    AND A202.indicator_name = N330000000000000.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS N330000111111111
    ON N330000111111111.code = '330000111111111'
    AND A202.indicator_name = N330000111111111.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS N330000888888888
    ON N330000888888888.code = '330000888888888'
    AND A202.indicator_name = N330000888888888.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS N33000099999999C
    ON N33000099999999C.code = '33000099999999C'
    AND A202.indicator_name = N33000099999999C.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS N330108142944731
    ON N330108142944731.code = '330108142944731'
    AND A202.indicator_name = N330108142944731.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A011
    ON A011.code = 'A011'
    AND A202.indicator_name = A011.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A122
    ON A122.code = 'A122'
    AND A202.indicator_name = A122.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A12201
    ON A12201.code = 'A12201'
    AND A202.indicator_name = A12201.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A123
    ON A123.code = 'A123'
    AND A202.indicator_name = A123.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A302
    ON A302.code = 'A302'
    AND A202.indicator_name = A302.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3300
    ON A3300.code = 'A3300'
    AND A202.indicator_name = A3300.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3301
    ON A3301.code = 'A3301'
    AND A202.indicator_name = A3301.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3302
    ON A3302.code = 'A3302'
    AND A202.indicator_name = A3302.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3303
    ON A3303.code = 'A3303'
    AND A202.indicator_name = A3303.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3304
    ON A3304.code = 'A3304'
    AND A202.indicator_name = A3304.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3305
    ON A3305.code = 'A3305'
    AND A202.indicator_name = A3305.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3306
    ON A3306.code = 'A3306'
    AND A202.indicator_name = A3306.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3307
    ON A3307.code = 'A3307'
    AND A202.indicator_name = A3307.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3308
    ON A3308.code = 'A3308'
    AND A202.indicator_name = A3308.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3309
    ON A3309.code = 'A3309'
    AND A202.indicator_name = A3309.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3310
    ON A3310.code = 'A3310'
    AND A202.indicator_name = A3310.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3311
    ON A3311.code = 'A3311'
    AND A202.indicator_name = A3311.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3312
    ON A3312.code = 'A3312'
    AND A202.indicator_name = A3312.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3314
    ON A3314.code = 'A3314'
    AND A202.indicator_name = A3314.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3315
    ON A3315.code = 'A3315'
    AND A202.indicator_name = A3315.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3317
    ON A3317.code = 'A3317'
    AND A202.indicator_name = A3317.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3318
    ON A3318.code = 'A3318'
    AND A202.indicator_name = A3318.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3351
    ON A3351.code = 'A3351'
    AND A202.indicator_name = A3351.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3352
    ON A3352.code = 'A3352'
    AND A202.indicator_name = A3352.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A368
    ON A368.code = 'A368'
    AND A202.indicator_name = A368.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A392
    ON A392.code = 'A392'
    AND A202.indicator_name = A392.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS B011
    ON B011.code = 'B011'
    AND A202.indicator_name = B011.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS B3300
    ON B3300.code = 'B3300'
    AND A202.indicator_name = B3300.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS B3301
    ON B3301.code = 'B3301'
    AND A202.indicator_name = B3301.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS B3302
    ON B3302.code = 'B3302'
    AND A202.indicator_name = B3302.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS B3303
    ON B3303.code = 'B3303'
    AND A202.indicator_name = B3303.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS B3304
    ON B3304.code = 'B3304'
    AND A202.indicator_name = B3304.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS B3305
    ON B3305.code = 'B3305'
    AND A202.indicator_name = B3305.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS B3306
    ON B3306.code = 'B3306'
    AND A202.indicator_name = B3306.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS B3307
    ON B3307.code = 'B3307'
    AND A202.indicator_name = B3307.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS B3308
    ON B3308.code = 'B3308'
    AND A202.indicator_name = B3308.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS B3309
    ON B3309.code = 'B3309'
    AND A202.indicator_name = B3309.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS B3310
    ON B3310.code = 'B3310'
    AND A202.indicator_name = B3310.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS B3311
    ON B3311.code = 'B3311'
    AND A202.indicator_name = B3311.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS B3312
    ON B3312.code = 'B3312'
    AND A202.indicator_name = B3312.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3300
    ON H3300.code = 'H3300'
    AND A202.indicator_name = H3300.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3301
    ON H3301.code = 'H3301'
    AND A202.indicator_name = H3301.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3302
    ON H3302.code = 'H3302'
    AND A202.indicator_name = H3302.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3303
    ON H3303.code = 'H3303'
    AND A202.indicator_name = H3303.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3304
    ON H3304.code = 'H3304'
    AND A202.indicator_name = H3304.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3305
    ON H3305.code = 'H3305'
    AND A202.indicator_name = H3305.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3306
    ON H3306.code = 'H3306'
    AND A202.indicator_name = H3306.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3307
    ON H3307.code = 'H3307'
    AND A202.indicator_name = H3307.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3308
    ON H3308.code = 'H3308'
    AND A202.indicator_name = H3308.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3309
    ON H3309.code = 'H3309'
    AND A202.indicator_name = H3309.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3310
    ON H3310.code = 'H3310'
    AND A202.indicator_name = H3310.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3311
    ON H3311.code = 'H3311'
    AND A202.indicator_name = H3311.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3312
    ON H3312.code = 'H3312'
    AND A202.indicator_name = H3312.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3313
    ON H3313.code = 'H3313'
    AND A202.indicator_name = H3313.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3314
    ON H3314.code = 'H3314'
    AND A202.indicator_name = H3314.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3315
    ON H3315.code = 'H3315'
    AND A202.indicator_name = H3315.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3920
    ON W3920.code = 'W3920'
    AND A202.indicator_name = W3920.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3921
    ON W3921.code = 'W3921'
    AND A202.indicator_name = W3921.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3922
    ON W3922.code = 'W3922'
    AND A202.indicator_name = W3922.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3923
    ON W3923.code = 'W3923'
    AND A202.indicator_name = W3923.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3924
    ON W3924.code = 'W3924'
    AND A202.indicator_name = W3924.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3925
    ON W3925.code = 'W3925'
    AND A202.indicator_name = W3925.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3926
    ON W3926.code = 'W3926'
    AND A202.indicator_name = W3926.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3927
    ON W3927.code = 'W3927'
    AND A202.indicator_name = W3927.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3928
    ON W3928.code = 'W3928'
    AND A202.indicator_name = W3928.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3929
    ON W3929.code = 'W3929'
    AND A202.indicator_name = W3929.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W392A
    ON W392A.code = 'W392A'
    AND A202.indicator_name = W392A.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W392B
    ON W392B.code = 'W392B'
    AND A202.indicator_name = W392B.indicator_name
WHERE A202.code = 'A202';



SELECT
  A202.indicator_name AS indicator_name,
  A202.sheet_name_a AS sheet_name_a,
  A202.index_title_a AS index_title_a,
  A202.product_name_a AS product_name_a,
  A202.sheet_name_b AS sheet_name_b,
  A202.index_title_b AS index_title_b,
  A202.product_name_b AS product_name_b,
  A202.increase_a AS increase_a_A202,
  A202.increase_b AS increase_b_A202,
  A011.increase_a AS increase_a_A011,
  A011.increase_b AS increase_b_A011,
  A122.increase_a AS increase_a_A122,
  A122.increase_b AS increase_b_A122,
  A12201.increase_a AS increase_a_A12201,
  A12201.increase_b AS increase_b_A12201,
  A123.increase_a AS increase_a_A123,
  A123.increase_b AS increase_b_A123,
  A302.increase_a AS increase_a_A302,
  A302.increase_b AS increase_b_A302,
  A3300.increase_a AS increase_a_A3300,
  A3300.increase_b AS increase_b_A3300,
  A3301.increase_a AS increase_a_A3301,
  A3301.increase_b AS increase_b_A3301,
  A3302.increase_a AS increase_a_A3302,
  A3302.increase_b AS increase_b_A3302,
  A3303.increase_a AS increase_a_A3303,
  A3303.increase_b AS increase_b_A3303,
  A3304.increase_a AS increase_a_A3304,
  A3304.increase_b AS increase_b_A3304,
  A3305.increase_a AS increase_a_A3305,
  A3305.increase_b AS increase_b_A3305,
  A3306.increase_a AS increase_a_A3306,
  A3306.increase_b AS increase_b_A3306,
  A3307.increase_a AS increase_a_A3307,
  A3307.increase_b AS increase_b_A3307,
  A3308.increase_a AS increase_a_A3308,
  A3308.increase_b AS increase_b_A3308,
  A3309.increase_a AS increase_a_A3309,
  A3309.increase_b AS increase_b_A3309,
  A3310.increase_a AS increase_a_A3310,
  A3310.increase_b AS increase_b_A3310,
  A3311.increase_a AS increase_a_A3311,
  A3311.increase_b AS increase_b_A3311,
  A3312.increase_a AS increase_a_A3312,
  A3312.increase_b AS increase_b_A3312,
  A3314.increase_a AS increase_a_A3314,
  A3314.increase_b AS increase_b_A3314,
  A3315.increase_a AS increase_a_A3315,
  A3315.increase_b AS increase_b_A3315,
  A3317.increase_a AS increase_a_A3317,
  A3317.increase_b AS increase_b_A3317,
  A3318.increase_a AS increase_a_A3318,
  A3318.increase_b AS increase_b_A3318,
  A3351.increase_a AS increase_a_A3351,
  A3351.increase_b AS increase_b_A3351,
  A3352.increase_a AS increase_a_A3352,
  A3352.increase_b AS increase_b_A3352,
  A368.increase_a AS increase_a_A368,
  A368.increase_b AS increase_b_A368,
  A392.increase_a AS increase_a_A392,
  A392.increase_b AS increase_b_A392,
  H3300.increase_a AS increase_a_H3300,
  H3300.increase_b AS increase_b_H3300,
  H3301.increase_a AS increase_a_H3301,
  H3301.increase_b AS increase_b_H3301,
  H3302.increase_a AS increase_a_H3302,
  H3302.increase_b AS increase_b_H3302,
  H3303.increase_a AS increase_a_H3303,
  H3303.increase_b AS increase_b_H3303,
  H3304.increase_a AS increase_a_H3304,
  H3304.increase_b AS increase_b_H3304,
  H3305.increase_a AS increase_a_H3305,
  H3305.increase_b AS increase_b_H3305,
  H3306.increase_a AS increase_a_H3306,
  H3306.increase_b AS increase_b_H3306,
  H3307.increase_a AS increase_a_H3307,
  H3307.increase_b AS increase_b_H3307,
  H3308.increase_a AS increase_a_H3308,
  H3308.increase_b AS increase_b_H3308,
  H3309.increase_a AS increase_a_H3309,
  H3309.increase_b AS increase_b_H3309,
  H3310.increase_a AS increase_a_H3310,
  H3310.increase_b AS increase_b_H3310,
  H3311.increase_a AS increase_a_H3311,
  H3311.increase_b AS increase_b_H3311,
  H3312.increase_a AS increase_a_H3312,
  H3312.increase_b AS increase_b_H3312,
  H3313.increase_a AS increase_a_H3313,
  H3313.increase_b AS increase_b_H3313,
  H3314.increase_a AS increase_a_H3314,
  H3314.increase_b AS increase_b_H3314,
  H3315.increase_a AS increase_a_H3315,
  H3315.increase_b AS increase_b_H3315,
  W3920.increase_a AS increase_a_W3920,
  W3920.increase_b AS increase_b_W3920,
  W3921.increase_a AS increase_a_W3921,
  W3921.increase_b AS increase_b_W3921,
  W3922.increase_a AS increase_a_W3922,
  W3922.increase_b AS increase_b_W3922,
  W3923.increase_a AS increase_a_W3923,
  W3923.increase_b AS increase_b_W3923,
  W3924.increase_a AS increase_a_W3924,
  W3924.increase_b AS increase_b_W3924,
  W3925.increase_a AS increase_a_W3925,
  W3925.increase_b AS increase_b_W3925,
  W3926.increase_a AS increase_a_W3926,
  W3926.increase_b AS increase_b_W3926,
  W3927.increase_a AS increase_a_W3927,
  W3927.increase_b AS increase_b_W3927,
  W3928.increase_a AS increase_a_W3928,
  W3928.increase_b AS increase_b_W3928,
  W3929.increase_a AS increase_a_W3929,
  W3929.increase_b AS increase_b_W3929,
  W392A.increase_a AS increase_a_W392A,
  W392A.increase_b AS increase_b_W392A,
  W392B.increase_a AS increase_a_W392B,
  W392B.increase_b AS increase_b_W392B
FROM
  invoice.bi_income_cost_custom AS A202
  LEFT JOIN invoice.bi_income_cost_custom AS A011
    ON A011.code = 'A011'
    AND A202.indicator_name = A011.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A122
    ON A122.code = 'A122'
    AND A202.indicator_name = A122.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A12201
    ON A12201.code = 'A12201'
    AND A202.indicator_name = A12201.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A123
    ON A123.code = 'A123'
    AND A202.indicator_name = A123.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A302
    ON A302.code = 'A302'
    AND A202.indicator_name = A302.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3300
    ON A3300.code = 'A3300'
    AND A202.indicator_name = A3300.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3301
    ON A3301.code = 'A3301'
    AND A202.indicator_name = A3301.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3302
    ON A3302.code = 'A3302'
    AND A202.indicator_name = A3302.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3303
    ON A3303.code = 'A3303'
    AND A202.indicator_name = A3303.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3304
    ON A3304.code = 'A3304'
    AND A202.indicator_name = A3304.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3305
    ON A3305.code = 'A3305'
    AND A202.indicator_name = A3305.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3306
    ON A3306.code = 'A3306'
    AND A202.indicator_name = A3306.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3307
    ON A3307.code = 'A3307'
    AND A202.indicator_name = A3307.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3308
    ON A3308.code = 'A3308'
    AND A202.indicator_name = A3308.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3309
    ON A3309.code = 'A3309'
    AND A202.indicator_name = A3309.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3310
    ON A3310.code = 'A3310'
    AND A202.indicator_name = A3310.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3311
    ON A3311.code = 'A3311'
    AND A202.indicator_name = A3311.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3312
    ON A3312.code = 'A3312'
    AND A202.indicator_name = A3312.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3314
    ON A3314.code = 'A3314'
    AND A202.indicator_name = A3314.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3315
    ON A3315.code = 'A3315'
    AND A202.indicator_name = A3315.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3317
    ON A3317.code = 'A3317'
    AND A202.indicator_name = A3317.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3318
    ON A3318.code = 'A3318'
    AND A202.indicator_name = A3318.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3351
    ON A3351.code = 'A3351'
    AND A202.indicator_name = A3351.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A3352
    ON A3352.code = 'A3352'
    AND A202.indicator_name = A3352.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A368
    ON A368.code = 'A368'
    AND A202.indicator_name = A368.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS A392
    ON A392.code = 'A392'
    AND A202.indicator_name = A392.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3300
    ON H3300.code = 'H3300'
    AND A202.indicator_name = H3300.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3301
    ON H3301.code = 'H3301'
    AND A202.indicator_name = H3301.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3302
    ON H3302.code = 'H3302'
    AND A202.indicator_name = H3302.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3303
    ON H3303.code = 'H3303'
    AND A202.indicator_name = H3303.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3304
    ON H3304.code = 'H3304'
    AND A202.indicator_name = H3304.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3305
    ON H3305.code = 'H3305'
    AND A202.indicator_name = H3305.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3306
    ON H3306.code = 'H3306'
    AND A202.indicator_name = H3306.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3307
    ON H3307.code = 'H3307'
    AND A202.indicator_name = H3307.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3308
    ON H3308.code = 'H3308'
    AND A202.indicator_name = H3308.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3309
    ON H3309.code = 'H3309'
    AND A202.indicator_name = H3309.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3310
    ON H3310.code = 'H3310'
    AND A202.indicator_name = H3310.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3311
    ON H3311.code = 'H3311'
    AND A202.indicator_name = H3311.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3312
    ON H3312.code = 'H3312'
    AND A202.indicator_name = H3312.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3313
    ON H3313.code = 'H3313'
    AND A202.indicator_name = H3313.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3314
    ON H3314.code = 'H3314'
    AND A202.indicator_name = H3314.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS H3315
    ON H3315.code = 'H3315'
    AND A202.indicator_name = H3315.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3920
    ON W3920.code = 'W3920'
    AND A202.indicator_name = W3920.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3921
    ON W3921.code = 'W3921'
    AND A202.indicator_name = W3921.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3922
    ON W3922.code = 'W3922'
    AND A202.indicator_name = W3922.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3923
    ON W3923.code = 'W3923'
    AND A202.indicator_name = W3923.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3924
    ON W3924.code = 'W3924'
    AND A202.indicator_name = W3924.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3925
    ON W3925.code = 'W3925'
    AND A202.indicator_name = W3925.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3926
    ON W3926.code = 'W3926'
    AND A202.indicator_name = W3926.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3927
    ON W3927.code = 'W3927'
    AND A202.indicator_name = W3927.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3928
    ON W3928.code = 'W3928'
    AND A202.indicator_name = W3928.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W3929
    ON W3929.code = 'W3929'
    AND A202.indicator_name = W3929.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W392A
    ON W392A.code = 'W392A'
    AND A202.indicator_name = W392A.indicator_name
  LEFT JOIN invoice.bi_income_cost_custom AS W392B
    ON W392B.code = 'W392B'
    AND A202.indicator_name = W392B.indicator_name
WHERE A202.code = 'A202'