#!/usr/bin/env python
# -*- coding: utf-8 -*-
# 用于表1成本和表3支出 单表多维度字段

import os
from sqlalchemy import create_engine, text, extract
from urllib.parse import quote
import pymysql

# 将pymysql注册为MySQL的适配器
pymysql.install_as_MySQLdb()

business_map = {
    '通信主业收入明细表(新方案）': 'bi_income',
    '营业成本': 'bi_cost_business',
    '管理费用': 'bi_cost_manage',
    '销售费用': 'bi_cost_sale',
    '研发费用': 'bi_cost_research',
}

index_custom = [
    ['全屋WiFi', '通信主业收入明细表(新方案）', '2.3.4.1、2.3.4.2'],
    ['天翼云盘', '通信主业收入明细表(新方案）', '1.5.1.1、3.1.1.3.1、3.1.2.3.1'],
    ['标准云产品', '通信主业收入明细表(新方案）', '3.1.1、3.1.2'],
    ['主机托管', '通信主业收入明细表(新方案）', '*******.1、*******.1'],
    ['宽带端口', '通信主业收入明细表(新方案）', '*******.2、*******.2'],
    ['集成服务', '通信主业收入明细表(新方案）', '3.1.3、3.1.4、3.2.4、3.2.5、3.3、3.4、3.5、3.6、3.7'],
    ['营业成本-人工成本', '营业成本', '1.1、1.2、1.3、1.4、1.5、1.6、1.7、1.8、1.10、1.11'],
    ['管理费用-人工成本', '管理费用', '1.1、1.2、1.3、1.4、1.5、1.6、1.7、1.8、1.9、1.10、1.11、1.12、1.13、1.14'],
]


def get_sqlalchemy_engine():
    host = '*************'
    user = 'pjadmin'
    password = 'Rt6#9Tae1'
    database = 'invoice'
    port = '8921'
    encoded_password = quote(password)
    SQLALCHEMY_DATABASE_URI = f'mysql://{user}:{encoded_password}@{host}:{port}/{database}'
    engine = create_engine(SQLALCHEMY_DATABASE_URI, pool_size=20, max_overflow=50)
    return engine


def get_sql_result(sql, **kwargs):
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    num = 3
    while num > 0:
        try:
            with engine.connect() as connection:
                result = connection.execute(text(sql), parameters=kwargs)
                rows = result.fetchall()
            break
        except Exception as e:
            print(e)
            num -= 1
    return rows


def execute_sql(sql, **kwargs):
    sql = sql.replace(':', r'\:')
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    num = 3
    while num > 0:
        try:
            with engine.connect() as connection:
                connection.execute(text(sql), parameters=kwargs)
                connection.commit()
            break
        except Exception as e:
            print(e)
            num -= 1

    return True


def create_income_cost_multi(month):
    bi_income_cost_sql = ''
    for line in index_custom:
        cols_data = []
        product_name, sheet_name, index_title = line[:]
        table_name = business_map[sheet_name]
        index_title_list = index_title.split('、')
        if len(index_title_list) == 1:
            index_query = f"='{index_title_list[0]}'"
        else:
            index_query = f' in {tuple(index_title_list)}'
        sql_query = f"select SUM(period_yestd) as `period_yestd`, SUM(occurrences_month) as `occurrences_month`, SUM(cumulative_year) as `cumulative_year`, `code`, `company`, `month_num`, `date_month` from {table_name} where date_month='{month}' and index_title{index_query} GROUP BY `code`,`date_month`"
        rows = get_sql_result(sql_query)
        for row in rows:
            period_yestd, occurrences_month, cumulative_year, code, company, month_num, date_month = row[:]
            if period_yestd is None or period_yestd == 0 or cumulative_year is None or cumulative_year == 0:
                increase = 0
            else:
                increase = cumulative_year / period_yestd - 1
            line_data = [index_title, product_name, period_yestd, occurrences_month, cumulative_year, increase, code, company, month_num, date_month]
            cols_data.append(f"{tuple(line_data)}")
        sql_custom = "insert into %s (`index_title`, `product_name`, `period_yestd`, `occurrences_month`, `cumulative_year`, `increase`, `code`, `company`, `month_num`, `date_month`) values %s;" % (
            table_name, ','.join(cols_data),)
        sql_custom = sql_custom.replace('None', 'null')
        # execute_sql(sql_custom)
        bi_income_cost_sql = bi_income_cost_sql + "\n" + sql_custom
    print(f"收入成本多维度-{month}处理完毕")
    return bi_income_cost_sql


if __name__ == '__main__':
    sql_custom_multi = create_income_cost_multi('2024-12')
    print('sql生成完成')
