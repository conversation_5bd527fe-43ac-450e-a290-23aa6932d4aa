drop table if exists `bi_income_B3312`;
drop table if exists `bi_income_B3311`;
drop table if exists `bi_income_B3310`;
drop table if exists `bi_income_B3309`;
drop table if exists `bi_income_B3308`;
drop table if exists `bi_income_B3307`;
drop table if exists `bi_income_B3306`;
drop table if exists `bi_income_B3305`;
drop table if exists `bi_income_B3304`;
drop table if exists `bi_income_B3303`;
drop table if exists `bi_income_B3302`;
drop table if exists `bi_income_B3301`;
drop table if exists `bi_income_B3300`;
drop table if exists `bi_income_B011`;
drop table if exists `bi_income_330000000000000`;
drop table if exists `bi_income_A368`;
drop table if exists `bi_income_330108142944731`;
drop table if exists `bi_income_A123`;
drop table if exists `bi_income_A3352`;
drop table if exists `bi_income_A3351`;
drop table if exists `bi_income_A3318`;
drop table if exists `bi_income_A3317`;
drop table if exists `bi_income_A3315`;
drop table if exists `bi_income_A3314`;
drop table if exists `bi_income_A3312`;
drop table if exists `bi_income_A3311`;
drop table if exists `bi_income_A3310`;
drop table if exists `bi_income_A3309`;
drop table if exists `bi_income_A3308`;
drop table if exists `bi_income_A3307`;
drop table if exists `bi_income_A3306`;
drop table if exists `bi_income_A3305`;
drop table if exists `bi_income_A3304`;
drop table if exists `bi_income_A3303`;
drop table if exists `bi_income_A3302`;
drop table if exists `bi_income_A3301`;
drop table if exists `bi_income_A3300`;
drop table if exists `bi_income_A011`;
drop table if exists `bi_income_330000111111111`;
drop table if exists `bi_income_330000888888888`;
drop table if exists `bi_income_33000099999999C`;
drop table if exists `bi_income_H3315`;
drop table if exists `bi_income_H3314`;
drop table if exists `bi_income_H3313`;
drop table if exists `bi_income_H3312`;
drop table if exists `bi_income_H3311`;
drop table if exists `bi_income_H3310`;
drop table if exists `bi_income_H3309`;
drop table if exists `bi_income_H3308`;
drop table if exists `bi_income_H3307`;
drop table if exists `bi_income_H3306`;
drop table if exists `bi_income_H3305`;
drop table if exists `bi_income_H3304`;
drop table if exists `bi_income_H3303`;
drop table if exists `bi_income_H3302`;
drop table if exists `bi_income_H3301`;
drop table if exists `bi_income_H3300`;
drop table if exists `bi_income_A12201`;
drop table if exists `bi_income_A122`;
drop table if exists `bi_income_A302`;
drop table if exists `bi_income_W392B`;
drop table if exists `bi_income_W392A`;
drop table if exists `bi_income_W3929`;
drop table if exists `bi_income_W3928`;
drop table if exists `bi_income_W3927`;
drop table if exists `bi_income_W3926`;
drop table if exists `bi_income_W3925`;
drop table if exists `bi_income_W3924`;
drop table if exists `bi_income_W3923`;
drop table if exists `bi_income_W3922`;
drop table if exists `bi_income_W3921`;
drop table if exists `bi_income_W3920`;
drop table if exists `bi_income_A392`;
drop table if exists `bi_income_A202`;
