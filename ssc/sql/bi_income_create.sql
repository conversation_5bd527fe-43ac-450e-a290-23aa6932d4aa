
CREATE TABLE IF NOT EXISTS `bi_income_A202` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A392` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_W3920` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_W3921` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_W3922` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_W3923` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_W3924` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_W3925` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_W3926` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_W3927` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_W3928` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_W3929` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_W392A` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_W392B` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A302` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A122` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A12201` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_H3300` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_H3301` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_H3302` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_H3303` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_H3304` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_H3305` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_H3306` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_H3307` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_H3308` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_H3309` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_H3310` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_H3311` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_H3312` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_H3313` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_H3314` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_H3315` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_33000099999999C` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_330000888888888` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_330000111111111` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A011` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3300` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3301` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3302` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3303` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3304` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3305` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3306` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3307` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3308` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3309` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3310` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3311` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3312` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3314` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3315` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3317` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3318` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3351` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A3352` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A123` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_330108142944731` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_A368` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_330000000000000` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_B011` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_B3300` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_B3301` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_B3302` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_B3303` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_B3304` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_B3305` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_B3306` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_B3307` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_B3308` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_B3309` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_B3310` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_B3311` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `bi_income_B3312` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_title` varchar(20) NOT NULL COMMENT '编号',
  `product_name` varchar(100) NOT NULL COMMENT '产品名称',
  `period_yestd` float NULL COMMENT '上年同期数',
  `occurrences_month` float NULL COMMENT '本月发生数',
  `cumulative_year` float NULL COMMENT '本年累计数',
  `increase` float NULL COMMENT '增幅',
  `code` varchar(100) NOT NULL COMMENT '公司编码',
  `company` varchar(100) NOT NULL COMMENT '公司名称',
  `date_month` varchar(20) NOT NULL COMMENT '年月',

  PRIMARY KEY (`id`),
  CONSTRAINT unique_index_month UNIQUE (`index_title`, `date_month`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

