#!/usr/bin/env python
# -*- coding: utf-8 -*-

from openpyxl import load_workbook,Workbook
import os
import shutil

from openpyxl.styles import Border, Side, colors, Font, Alignment, PatternFill
from openpyxl.utils import get_column_letter

# 加边框
border = Border(top=Side(border_style='thin', color=colors.BLACK),
                bottom=Side(border_style='thin', color=colors.BLACK),
                left=Side(border_style='thin', color=colors.BLACK),
                right=Side(border_style='thin', color=colors.BLACK))

# # 设置单元格内文本的对齐方式
alignment = Alignment(horizontal='center', vertical='center', text_rotation=0, wrap_text=True)

alignment_r = Alignment(horizontal='right', text_rotation=0, wrap_text=True)

bgColor = '70AD47'
fill = PatternFill(start_color=bgColor, end_color=bgColor, fill_type='solid')
bgColor2 = 'F4B084'
fill_red = PatternFill(start_color=bgColor2, end_color=bgColor2, fill_type='solid')

def excelstyle_base(cell,value):
    cell.value = value
    cell.border = border
    cell.alignment = alignment
    cell.font = Font(size=9, name='宋体', bold=False)
    # return cell

def excelstyle_merge(cell,value):
    cell.value = value
    cell.border = border
    cell.alignment = alignment
    cell.font = Font(size=9, name='宋体', bold=True) # 粗体
    # return cell

def excelstyle_data(cell,value):
    cell.value = value
    cell.border = border
    cell.alignment = alignment_r
    cell.font = Font(size=9, name='Arial', bold=False)