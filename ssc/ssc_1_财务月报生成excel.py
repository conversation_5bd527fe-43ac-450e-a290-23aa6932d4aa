# 第三方模块，作用域：操作系统进行交互的模块
import os
# 第三方模块，作用域：传递部分参数
import sys
# 第三方模块，作用域：日期、时间
import time
# 第三方模块，作用域：表格处理
import pandas as pd
import re
import random
import zipfile

# 需要把1.33.0升级成1.49.0
from playwright.sync_api import sync_playwright
from cryptography.fernet import Fernet
from sqlalchemy import create_engine, text
from urllib.parse import quote
import pymysql

# 用于匹配数据库信息
robot_id = 'sac_1'

key = b'qK23mUVUQ_3zNMEZcSCUfYXku29QT7AXxuggFfa-gFo='
cipher_suite = Fernet(key)
# 将pymysql注册为MySQL的适配器
pymysql.install_as_MySQLdb()


def get_sqlalchemy_engine():
    # 获取数据库连接池引擎
    host = '**************'
    user = 'root'
    password = '08@3FYm*zp+NX3%e'
    encoded_password = quote(password)
    database = 'rpa'
    port = '8866'
    SQLALCHEMY_DATABASE_URI = f'mysql://{user}:{encoded_password}@{host}:{port}/{database}'
    engine = create_engine(SQLALCHEMY_DATABASE_URI, pool_size=20, max_overflow=50)
    return engine


def get_sql_result(sql, **kwargs):
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    with engine.connect() as connection:
        result = connection.execute(text(sql), parameters=kwargs)
        rows = result.fetchall()
    return rows


def execute_sql(sql, **kwargs):
    sql = sql.replace(':', r'\:')
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    with engine.connect() as connection:
        connection.execute(text(sql), parameters=kwargs)
        connection.commit()
    return True


def unzip_file(zip_file_path, extract_path=None):
    file_name = os.path.splitext(os.path.basename(zip_file_path))[0]
    if not extract_path:
        extract_path = os.path.join(os.path.dirname(zip_file_path), file_name)
    if not os.path.exists(extract_path):
        with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
            zip_ref.extractall(extract_path)
    return extract_path, file_name


def login_by_account(page, login, account, password):
    """
    账号密码登录
    """
    page.goto(login)
    page.wait_for_load_state()
    page.fill("#freename", account)
    page.fill("#freepassword", password)
    # 执行登录
    page.click('#login_btnDiv > input')
    page.wait_for_load_state()
    return page


def run(playwright, path=None, id=None, url=None, download=True):
    file_path = ''
    if download:
        if path:
            # 使用平台传入浏览器前缀 统一路径
            executable_path = path + r'\Lib\site-packages\ms-playwright\firefox-1327\firefox\firefox.exe'
        else:
            # 使用本机绝对路径
            # executable_path = r'C:\Program Files\HyperActuator\resources\Python39\Lib\site-packages\ms-playwright\chromium-1060\chrome-win\chrome.exe'
            executable_path = r'C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1148\chrome-win\chrome.exe'
            if not os.path.exists(executable_path):  # 判断释放存在文件
                print('请检查本机驱动路径是否存在，处理方案：桌面右键打开【助手/执行器】文件所在位置，替换executable_path参数的前缀后，重新运行！')
                return

        # file_path = "D:\data\人员账号信息.xlsx"
        # account_info = load_account_info(file_path)
        sql = f"SELECT `username`,`password` FROM rpa_users WHERE robot_id='{robot_id}' AND status='Enable'"
        account_info = get_sql_result(sql)

        for info in account_info:
            encrypted_msg = info[1]
            decrypted_msg = cipher_suite.decrypt(encrypted_msg).decode('utf-8')
            # 初始化浏览器对象 headless：False有头模式、True无头模式，executable_path浏览器路径
            # browser = playwright.firefox.launch(headless=False, executable_path=executable_path)
            browser = playwright.chromium.launch(headless=False, executable_path=executable_path)
            # 创建下上文环境，作用域：运行过程只要结束掉 则会关掉浏览器 释放内存
            context = browser.new_context()
            # 实例化页面对象 新建浏览器窗口页面
            page = context.new_page()
            # 跳转对应浏览器地址并登录
            page = login_by_account(page, "http://sso.zjtelecom.cn/cas/login", info[0], decrypted_msg)
            # eip_login(page, info["账号"], info["密码"])
            time.sleep(2)
            page.get_by_text("集团应用").click()
            with page.expect_popup() as page1_info:
                page.get_by_text("合并报表（新）").click()
            page1 = page1_info.value
            page1.wait_for_load_state(state="networkidle")
            page1.get_by_text("数据管理").click()
            page1.get_by_text("批量导出").click()
            time.sleep(1)
            # 导出
            page1.get_by_text("选择报表").click()
            page1.locator("div").filter(has_text=re.compile(r"^已选择 0 张报表$")).locator("i").click()
            time.sleep(5)
            page1.get_by_text("全清").click()


            # items = [9, 15, 17, 18, 19]
            # for i in items:
            #     page1.locator(
            #         f"div:nth-child({i}) > .i-node-wrapper-cls > .i-node-content > .checkbox-cls > .ivu-checkbox-wrapper > .ivu-checkbox > .ivu-checkbox-input").check()

            items = ["通信主业收入明细表(新方案）", "营业成本", "销售费用", "管理费用", "研发费用"]
            total = page1.locator(
                f"div > .i-node-wrapper-cls > .i-node-content > .checkbox-cls > .ivu-checkbox-wrapper > .ivu-checkbox > .ivu-checkbox-input")
            for index in range(total.count()):
                show_text = page1.locator(
                    f"div:nth-child({index+1}) > .i-node-wrapper-cls > .i-node-content > .title-cls > .show-text > span").inner_text()
                print(show_text)
                if show_text in items:
                    page1.locator(
                        f"div:nth-child({index+1}) > .i-node-wrapper-cls > .i-node-content > .checkbox-cls > .ivu-checkbox-wrapper > .ivu-checkbox > .ivu-checkbox-input").check()
                    print(f"---已选择{show_text}")

            page1.get_by_role("button", name="确定").click()
            page1.get_by_label("按报表生成文件").check()
            page1.get_by_role("button", name="开始执行").click()
            # time.sleep(2*len(items))
            with page1.expect_download() as download_info:
                page1.get_by_role("button", name="下载").click()
            download = download_info.value
            name = download.suggested_filename  # get suggested name
            file_path = f"D:\代码文档\RPA\light-edit\data\\{name}"  # file path
            download.save_as(file_path)

            time.sleep(2)
            try:
                context.close()
                browser.close()
            except:
                pass
    else:
        file_dir = "D:\代码文档\RPA\light-edit\data"
        file_lists = os.listdir(file_dir)
        file_lists.sort(key=lambda fn: os.path.getmtime(file_dir + "\\" + fn) if not os.path.isdir(file_dir + "\\" + fn) else 0)
        file_path = os.path.join(file_dir, file_lists[-1])

    # 分析本地文件
    from report_analysis import report_export
    extract_path, file_name = unzip_file(file_path)
    report_export(extract_path)


if __name__ == '__main__':
    # 本地:获取当前文件夹 所在的本机绝对路径  平台:文件路径、浏览器路径、应用工作流唯一ID、服务端地址
    ley_list = sys.argv
    with sync_playwright() as playwright:
        if len(ley_list) > 1:
            path = ley_list[1]  # path:浏览器前缀统一路径
            id = ley_list[2]  # Id:应用工作流唯一ID
            url = ley_list[3]  # url:服务端地址
            run(playwright, path, id, url)
        else:
            # 本地执行
            run(playwright, download=True)
