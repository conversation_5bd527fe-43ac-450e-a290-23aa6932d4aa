# 履约计划发票校验

# 第三方模块，作用域：操作系统进行交互的模块
import os
# 第三方模块，作用域：传递部分参数
import sys
# 第三方模块，作用域：日期、时间
import time
# 第三方模块，作用域：表格处理
import pandas as pd

import random
import rpa_util
from datetime import datetime

# 装饰器，作用域：执行状态更改，平台部分校验通过
from merakwebroombiz.aop.api_aop import tasks_decorator

# 日志组件：1.执行步骤
from merakwebroombiz.logger.output_logger import output_logger as logger
# 需要把1.33.0升级成1.49.0
from playwright.sync_api import sync_playwright
from cryptography.fernet import Fernet
from sqlalchemy import create_engine, text
from urllib.parse import quote
import pymysql
# 御龙测试环境
from Crypto.Cipher import AES
import binascii
from Crypto.Util.Padding import pad, unpad


# 全局变量,用于上报数据
record_id = ''  # Id:应用工作流唯一ID
server_url = 'https://yl.public.zj.cn/automation-workflow/api/' # url:服务端地址
flow_para = ''  # 参数
wf_id = '1871458616424353793'  # 流程部署ID
wf_name = '履约计划仅发票校验'  # 流程名
rpa_code = 'invoice_verification'


class AESUtil:
    # 测试环境秘钥：16a41a967938d6428a89e5dca5350ae2
    # 正式环境秘钥：********************************
    aes_key = '********************************'

    def __init__(self, aes_key=''):
        if aes_key:
            self.aes_key = aes_key
        pass

    def encrypt(self, text):
        key = binascii.unhexlify(self.aes_key)
        cipher = AES.new(key, AES.MODE_ECB)  # 使用CBC模式，设置密钥和初始向量
        padded_text = pad(text.encode(), AES.block_size)  # 进行填充
        ciphertext = cipher.encrypt(padded_text)
        return binascii.hexlify(ciphertext).decode()

    def decrypt(self, encryptText):
        key = binascii.unhexlify(self.aes_key)
        cipher = AES.new(key, AES.MODE_ECB)
        ciphertext = binascii.unhexlify(encryptText)
        plaintext = cipher.decrypt(ciphertext)
        unpadded_plaintext = unpad(plaintext, AES.block_size)  # 去除填充
        return unpadded_plaintext.decode()

aes_util = AESUtil()
# 用于匹配数据库信息
robot_id = '1'
# 将pymysql注册为MySQL的适配器
pymysql.install_as_MySQLdb()


def get_sqlalchemy_engine():
    # 获取数据库连接池引擎
    # 御龙测试库
    # host='**************'
    # user='root'
    # password='Q/>l26yxzm5e'
    # database='pubinfo_digital'
    # port='3306'
    # 御龙线上库，只能服务器连接
    host = '*************'
    user = 'pubinfowx'
    password = 'P5xdp#yt6'
    database = 'pubinfo_digital'
    port = '6606'
    encoded_password = quote(password)
    SQLALCHEMY_DATABASE_URI = f'mysql://{user}:{encoded_password}@{host}:{port}/{database}'
    engine = create_engine(SQLALCHEMY_DATABASE_URI, pool_size=20, max_overflow=50)
    return engine


def get_sql_result(sql, **kwargs):
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    num = 3
    while num > 0:
        try:
            with engine.connect() as connection:
                result = connection.execute(text(sql), parameters=kwargs)
                rows = result.fetchall()
            break
        except Exception as e:
            print(e)
            num -= 1
    return rows


def execute_sql(sql, **kwargs):
    sql = sql.replace(':', r'\:')
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    num = 3
    while num > 0:
        try:
            with engine.connect() as connection:
                connection.execute(text(sql), parameters=kwargs)
                connection.commit()
            break
        except Exception as e:
            print(e)
            num -= 1

    return True


def get_platform_account_from_rpa(code, type='5'):
    sql = f"SELECT a.`id`, a.`platform_login_name`, a.`platform_password` FROM pub_platform_account AS a JOIN pub_user AS b JOIN rpa_process_user AS c WHERE a.type='{type}' AND a.user_id=b.id AND b.id = c.user_id and c.rpa_info_code='{code}'"
    res = get_sql_result(sql)
    user_list = []
    if res:
        for row in res:
            nid, platform_login_name, encrypted_platform_password = row[0], row[1], row[2]
            decrypted_platform_password = aes_util.decrypt(encrypted_platform_password)
            user_list.append([platform_login_name, decrypted_platform_password, nid])
    return user_list


def update_is_password_right_2(nid):
    sql = f"update pub_platform_account set is_password_right=2 where id={nid}"
    execute_sql(sql)
    return True


def get_platform_account(name, type='5'):
    sql = f"SELECT a.`platform_login_name`, a.`platform_password` FROM pub_platform_account AS a JOIN pub_user AS b WHERE a.type='{type}' AND a.user_id=b.id AND b.real_name='{name}'"
    res = get_sql_result(sql)
    user_list = []
    if res:
        for row in res:
            platform_login_name, encrypted_platform_password = row[0], row[1]
            decrypted_platform_password = aes_util.decrypt(encrypted_platform_password)
            user_list.append([platform_login_name, decrypted_platform_password])
    user_list = [['zengsy.xc', 'Zsy131426?']]
    return user_list


@tasks_decorator
def run(playwright, path=None, id=None, url=None):
    if path:
        # 使用平台传入浏览器前缀 统一路径
        executable_path = path + r'\Lib\site-packages\ms-playwright\chromium-1060\chrome-win\chrome.exe'
    else:
        # 使用本机绝对路径
        # executable_path = r'C:\Program Files\HyperActuator\resources\Python39\Lib\site-packages\ms-playwright\chromium-1060\chrome-win\chrome.exe'
        executable_path = r'C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1148\chrome-win\chrome.exe'
        if not os.path.exists(executable_path):  # 判断释放存在文件
            logger.error('请检查本机驱动路径是否存在，处理方案：桌面右键打开【助手/执行器】文件所在位置，替换executable_path参数的前缀后，重新运行！')
            return

    # sql = f"SELECT a.`platform_login_name`, a.`platform_password` FROM pub_platform_account AS a JOIN pub_user AS b WHERE a.type='5' AND a.user_id=b.id AND b.real_name='胡袁明'"
    # account_info = get_sql_result(sql)

    account_info = get_platform_account_from_rpa(rpa_code)
    logger.info(f'{len(account_info)}个用户启用了')
    for info in account_info:
        # 初始化浏览器对象 headless：False有头模式、True无头模式，executable_path浏览器路径
        # browser = playwright.firefox.launch(headless=False, executable_path=executable_path)
        browser = playwright.chromium.launch(headless=False, executable_path=executable_path)
        # 创建下上文环境，作用域：运行过程只要结束掉 则会关掉浏览器 释放内存
        context = browser.new_context()
        # 实例化页面对象 新建浏览器窗口页面
        page = context.new_page()
        # 跳转对应浏览器地址并登录
        page = login_by_account(page, "http://sso.zjtelecom.cn/cas/login", info[0], info[1])

        status = page.locator('//*[@id="status"]')
        print(status.is_visible())
        if status.is_visible():
            if '账号或密码错误' in status.inner_text() or '账户或密码已输入错误' in status.inner_text():
                update_is_password_right_2(info[2])
                logger.info(f'账号{info[0]}密码错误')
                continue

        # eip_login(page, info["账号"], info["密码"])
        time.sleep(2)
        # page1 = get_contract_info(page, '//img[contains(@src,"images/apps/app8.png")]')
        with page.expect_popup() as page1_info:
            page.locator("li").filter(has_text="集团财辅").get_by_role("img").click()
        page1 = page1_info.value
        try:
            page1.wait_for_load_state(state="networkidle")
        except:
            pass
        time.sleep(2)
        page1.get_by_role("link", name="报账平台").click()
        try:
            page1.wait_for_load_state()
        except:
            pass
        time.sleep(1)
        page1.locator("#leftside").content_frame.get_by_role("link", name="待办工作").click()
        page1.wait_for_load_state()
        time.sleep(1)
        page1.locator("iframe[name=\"rightside\"]").content_frame.get_by_role("link", name="高级查询").click()
        time.sleep(1)
        page1.locator("iframe[name=\"rightside\"]").content_frame.locator("#economicItemCode").fill("02-10070210")
        time.sleep(1)
        page1.locator("iframe[name=\"rightside\"]").content_frame.get_by_role("row",
                                                                              name="是否被共享:    查询").get_by_role(
            "link").click()
        time.sleep(1)
        count_link = 0
        while True:
            time.sleep(1)
            # 定位iframe元素，该iframe的name属性为"rightside"
            iframe_locator = page1.locator("iframe[name=\"rightside\"]").content_frame
            links = iframe_locator.locator('[id^="procName_"]').all()
            if len(links) == 0:
                print('页面无数据，退出')
                break

            for link in links:
                start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                text = link.inner_text()
                print(text)

                with page1.expect_popup() as page2_info:
                    link.click()
                page2 = page2_info.value
                page2.wait_for_load_state()
                time.sleep(2)
                # 处理页面元素
                ExpensesType = page2.locator("iframe[name=\"AppFrm\"]").content_frame.locator(
                    '//*[@id="writeoffBaseInfo"]/div[5]/table/tbody/tr/td[3]').inner_text()
                if ExpensesType != '仅发票校验':
                    print("报账类型不对，不处理")
                    continue
                # 是否退回订单
                text_back = page2.locator("iframe[name=\"AppFrm\"]").content_frame.locator(
                    '//*[@id="ModTab_txtL0"]/p[1]')
                if '不同意' in text_back.inner_text():
                    print("这是退回订单，不处理")
                    continue
                # 报账单号流程只提交一次
                OrderNumber = page2.locator("iframe[name=\"AppFrm\"]").content_frame.locator(
                    '//*[@id="mainDiv"]/div[3]/div/div[7]/div[2]').inner_text()
                sql = f"SELECT `flow_number` FROM rpa_flow WHERE robot_id={robot_id}"
                res = get_sql_result(sql)
                number_list = [item[0] for item in res]
                if OrderNumber in number_list:
                    print("报账单号已提交过一次，不处理")
                    continue
                # 校验数据部分
                page2.locator("iframe[name=\"AppFrm\"]").content_frame.get_by_role("cell", name="发票明细︽").click()
                noTaxSum = page2.locator("iframe[name=\"AppFrm\"]").content_frame.locator('//*[@name="noTaxSum"]')
                noTaxSum_int = float(noTaxSum.inner_text().replace(',', ''))
                taxSum = page2.locator("iframe[name=\"AppFrm\"]").content_frame.locator('//*[@name="taxSum"]')
                taxSum_int = float(taxSum.inner_text().replace(',', ''))
                invoiceSum = page2.locator("iframe[name=\"AppFrm\"]").content_frame.locator('//*[@name="invoiceSum"]')
                invoiceSum_int = float(invoiceSum.inner_text().replace(',', ''))

                amountSum = page2.locator("iframe[name=\"AppFrm\"]").content_frame.locator(
                    "#selectContentFrame").content_frame.locator('#amountSum')
                amountSum_int = float(amountSum.inner_text().replace(',', ''))
                taxAmountSum = page2.locator("iframe[name=\"AppFrm\"]").content_frame.locator(
                    "#selectContentFrame").content_frame.locator('#taxAmountSum')
                taxAmountSum_int = float(taxAmountSum.inner_text().replace(',', ''))
                totalSum = page2.locator("iframe[name=\"AppFrm\"]").content_frame.locator(
                    "#selectContentFrame").content_frame.locator('#totalSum')
                totalSum_int = float(totalSum.inner_text().replace(',', ''))
                # 订单收货金额减去已校验发票净额要等于本次发票净额，如果不相等，请在报账明细中写明:【请费用会计全额处理grir科目(金额等于订单收货金额），与发票价款差异部分挂库存成本差异科目】
                # 相差10元以内的提交
                OrderReceive = page2.locator("iframe[name=\"AppFrm\"]").content_frame.locator(
                    "iframe[name$=\"_check\"]").content_frame.locator("#detailInfo").locator("td").nth(3)
                OrderReceive_int = float(OrderReceive.inner_text().replace(',', ''))
                VerifiedInvoice = page2.locator("iframe[name=\"AppFrm\"]").content_frame.locator(
                    "iframe[name$=\"_check\"]").content_frame.locator('//*[@id="detailInfo"]/table/tbody/tr[2]/td[6]')
                VerifiedInvoice_int = float(VerifiedInvoice.inner_text().replace(',', ''))
                InvoiceNetThis = page2.locator("iframe[name=\"AppFrm\"]").content_frame.locator(
                    "iframe[name$=\"_check\"]").content_frame.locator('//*[@id="vo.writeoffDetails[-1].auditPriceSum"]')
                InvoiceNetThis_int = float(InvoiceNetThis.evaluate('(element) => element.value').replace(',', ''))

                status = 0
                if noTaxSum_int == amountSum_int and taxSum_int == taxAmountSum_int and invoiceSum_int == totalSum_int:
                    if OrderReceive_int - VerifiedInvoice_int == InvoiceNetThis_int:
                        status = 1
                    elif abs(OrderReceive_int - VerifiedInvoice_int - InvoiceNetThis_int) <= 10:
                        status = 2
                        # 填写报账说明
                        detail = page2.locator("iframe[name=\"AppFrm\"]").content_frame.locator(
                            '//*[@id="vo.writeoffInstanceVo.abstract_"]')
                        detail_str = detail.evaluate('(element) => element.value')
                        detail.fill(
                            f"{detail_str};【请费用会计全额处理grir科目(金额等于订单收货金额），与发票价款差异部分挂库存成本差异科目】")
                        page2.get_by_role("link", name="保存").click()
                    else:
                        print("金额异常，请手动处理")
                        biz_no = OrderNumber
                        user_name = info[0]
                        end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        rpa_util.data_report(biz_no, '0', start_time, end_time, wf_name, wf_id, server_url,
                                             user_name, f'发票金额差异', failType='1', rpaCode=rpa_code)

                if status > 0:
                    print(f"--{text}验证通过,准备提交")
                    page2.get_by_role("link", name="提交").click()
                    # 确认是否保存
                    page2.get_by_text("确定").click()
                    # 确认影像
                    page2.get_by_text("确定").click()
                    # 确认提交
                    page2.locator("iframe[name^=\"OpenartDialog\"]").content_frame.get_by_role("button", name="确认").click()
                    # 新增报账单号到数据库
                    sql = f"INSERT INTO rpa_flow (robot_id, flow_number) VALUES ('{robot_id}', '{OrderNumber}')"
                    execute_sql(sql)
                    count_link += 1

                    biz_no = OrderNumber
                    user_name = info[0]
                    end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    rpa_util.data_report(biz_no, '1', start_time, end_time, wf_name, wf_id, server_url,
                                         user_name, rpaCode=rpa_code)

                page2.close()

            # 判断是否有下一页
            try:
                flag = page1.locator("iframe[name=\"rightside\"]").content_frame.get_by_role("link", name="下一页")
                if flag.is_visible():
                    flag.click()
                    page1.wait_for_load_state()
                else:
                    print('没有下一页了，退出')
                    break
            except Exception as e:
                print(e)
                break
        logger.info(f'共处理{count_link}条数据')
        time.sleep(2)
        try:
            context.close()
            browser.close()
        except:
            pass

def get_contract_info(page,selector):
    """
    页面跳转
    """
    with page.expect_popup(timeout=50000) as new_page:
        el = page.query_selector(selector)
        el.click()
    page2 = new_page.value
    page2.wait_for_load_state()
    time.sleep(5)
    return page2

def login_by_account(page,login,account,password):
    """
    账号密码登录
    """
    page.goto(login)
    page.wait_for_load_state()
    page.fill("#freename", account)
    page.fill("#freepassword", password)
    # 执行登录
    page.click('#login_btnDiv > input')
    page.wait_for_load_state()
    return page

def load_account_info(file_path):
    sheet_name = 'Sheet1'
    # 读取人员账号信息Excel文件
    df = pd.read_excel(file_path, sheet_name=sheet_name)  # sheet_name=0表示第一个工作表，也可以使用工作表名称
    data = []
    for idx, r in df.iterrows():
        data.append(r)
    return data

def eip_login(page, eip_username, eip_password):
    eip_login_url = 'http://eip.zjtelecom.cn/pluto/portal/eipPortal.jhtml'
    try:
        page.goto(eip_login_url)
        if page.title() not in 'EIP SSO':
            page.evaluate('location.reload();')
        # 账号登录
        page.fill('[id="inputUserName"]', eip_username)
        print('输入账号')
        page.fill('[id="inputPwd"]', eip_password)
        print('输入密码')
        # 短信登录
        # page.fill('[id="inputPhoneNum"]', eip_username)
        # page.click('//div[@id="verifyCodeBox"]/label[text()="获取验证码"]')
        # 等待输入手机验证码
        # eip_verifycode = input("请输入手机验证码")
        # page.fill('[id="inputVerifyCode"]', eip_username)

        # 点击登录，因为要加载新页面，所以要用到页面导航方法
        with page.expect_navigation():
            page.click('[value="登 录"]')
            print('点击登录')
        time.sleep(random.randint(9, 10))

        for h in range(30):
            hello_el = page.query_selector('//p[contains(text(), "欢迎你:")]')
            if hello_el:
                print('登录成功')
                break
            time.sleep(1)
        time.sleep(2)
        print(page.context.cookies())
    except (Exception, TimeoutError) as e:
        print(f'login err:{e}')
        raise e

if __name__ == '__main__':
    # 本地:获取当前文件夹 所在的本机绝对路径  平台:文件路径、浏览器路径、应用工作流唯一ID、服务端地址
    ley_list = sys.argv
    with sync_playwright() as playwright:
        if len(ley_list) > 1:
            path = ley_list[1]  # path:浏览器前缀统一路径
            id = ley_list[2]  # Id:应用工作流唯一ID
            url = ley_list[3]  # url:服务端地址
            run(playwright,path, id, url)
        else:
            # 本地执行
            run(playwright)
