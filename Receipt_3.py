# 第三方模块，作用域：操作系统进行交互的模块
import os
# 第三方模块，作用域：传递部分参数
import sys
# 第三方模块，作用域：日期、时间
import time
# 第三方模块，作用域：表格处理
import pandas as pd

import random

import difflib

# 装饰器，作用域：执行状态更改，平台部分校验通过
from merakwebroombiz.aop.api_aop import tasks_decorator

# 日志组件：1.执行步骤
from merakwebroombiz.logger.output_logger import output_logger as logger
# 需要把1.33.0升级成1.49.0
from playwright.sync_api import sync_playwright
from cryptography.fernet import Fernet
from sqlalchemy import create_engine, text
from urllib.parse import quote
import pymysql

# 用于匹配数据库信息
robot_id = '3'

key = b'qK23mUVUQ_3zNMEZcSCUfYXku29QT7AXxuggFfa-gFo='
cipher_suite = Fernet(key)
# 将pymysql注册为MySQL的适配器
pymysql.install_as_MySQLdb()


def get_sqlalchemy_engine():
    # 获取数据库连接池引擎
    host = '**************'
    user = 'root'
    password = '08@3FYm*zp+NX3%e'
    encoded_password = quote(password)
    database = 'rpa'
    port = '8866'
    SQLALCHEMY_DATABASE_URI = f'mysql://{user}:{encoded_password}@{host}:{port}/{database}'
    engine = create_engine(SQLALCHEMY_DATABASE_URI, pool_size=20, max_overflow=50)
    return engine


def get_sql_result(sql, **kwargs):
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    with engine.connect() as connection:
        result = connection.execute(text(sql), parameters=kwargs)
        rows = result.fetchall()
    return rows


def execute_sql(sql, **kwargs):
    sql = sql.replace(':', r'\:')
    engine = get_sqlalchemy_engine()
    # 执行SQL查询
    with engine.connect() as connection:
        connection.execute(text(sql), parameters=kwargs)
        connection.commit()
    return True


def find_longest_common_subsequence(str1, str2):
    """
    找出两个字符串的最长公共子序列，并将其拼接成一个新的字符串。
    :param str1: 第一个字符串
    :param str2: 第二个字符串
    :return: 最长公共子序列组成的字符串
    """
    # 创建SequenceMatcher对象，用于比较两个字符串
    matcher = difflib.SequenceMatcher(None, str1, str2)

    # 获取最长公共子序列的匹配信息
    match = matcher.find_longest_match(0, len(str1), 0, len(str2))

    # 如果找到匹配，则返回公共子序列；否则返回空字符串
    if match.size > 0:
        return str1[match.a: match.a + match.size]
    else:
        return ""


# @tasks_decorator
def run(playwright, path=None, id=None, url=None):
    if path:
        # 使用平台传入浏览器前缀 统一路径
        executable_path = path + r'\Lib\site-packages\ms-playwright\firefox-1327\firefox\firefox.exe'
    else:
        # 使用本机绝对路径
        # executable_path = r'C:\Program Files\HyperActuator\resources\Python39\Lib\site-packages\ms-playwright\chromium-1060\chrome-win\chrome.exe'
        executable_path = r'C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1148\chrome-win\chrome.exe'
        if not os.path.exists(executable_path):  # 判断释放存在文件
            logger.error(
                '请检查本机驱动路径是否存在，处理方案：桌面右键打开【助手/执行器】文件所在位置，替换executable_path参数的前缀后，重新运行！')
            return

    # file_path = "D:\data\人员账号信息.xlsx"
    # account_info = load_account_info(file_path)
    sql = f"SELECT `username`,`password` FROM rpa_users WHERE robot_id={robot_id} AND status='Enable'"
    account_info = get_sql_result(sql)

    for info in account_info:
        encrypted_msg = info[1]
        decrypted_msg = cipher_suite.decrypt(encrypted_msg).decode('utf-8')
        # 初始化浏览器对象 headless：False有头模式、True无头模式，executable_path浏览器路径
        # browser = playwright.firefox.launch(headless=False, executable_path=executable_path)
        browser = playwright.chromium.launch(headless=False, executable_path=executable_path)
        # 创建下上文环境，作用域：运行过程只要结束掉 则会关掉浏览器 释放内存
        context = browser.new_context()
        # 实例化页面对象 新建浏览器窗口页面
        page = context.new_page()
        # 跳转对应浏览器地址并登录
        page = login_by_account(page, "http://sso.zjtelecom.cn/cas/login", info[0], decrypted_msg)
        # eip_login(page, info["账号"], info["密码"])
        time.sleep(2)
        # page1 = get_contract_info(page, '//img[contains(@src,"images/apps/app8.png")]')
        with page.expect_popup() as page1_info:
            page.locator("li").filter(has_text="集团财辅").get_by_role("img").click()
        page1 = page1_info.value
        page1.wait_for_load_state(state="networkidle")
        time.sleep(2)
        page1.get_by_role("link", name="报账平台").click()
        page1.wait_for_load_state()
        time.sleep(1)
        page1.locator("#leftside").content_frame.get_by_role("link", name="已办工作").click()
        page1.wait_for_load_state()
        time.sleep(1)
        page1.locator("iframe[name=\"rightside\"]").content_frame.get_by_role("link", name="高级查询").click()
        time.sleep(1)
        page1.locator("iframe[name=\"rightside\"]").content_frame.locator("#economicItemCode").fill("02-100505")
        page1.locator("iframe[name=\"rightside\"]").content_frame.locator("[id=\"业务场景\"]").select_option("4")
        time.sleep(1)
        page1.locator("iframe[name=\"rightside\"]").content_frame.get_by_role("row",
                                                                              name="是否被共享:    查询").get_by_role(
            "link").click()
        time.sleep(1)
        # rows = page.query_selector_all('table tr')
        while True:
            time.sleep(1)
            # 定位iframe元素，该iframe的name属性为"rightside"
            iframe_locator = page1.locator("iframe[name=\"rightside\"]").content_frame
            links = iframe_locator.locator('[id^="procName_"]').all()
            for link in links:
                text = link.inner_text()
                print("######################################################")
                print(text)

                with page1.expect_popup() as page2_info:
                    link.click()
                page2 = page2_info.value
                page2.wait_for_load_state()
                time.sleep(2)
                # 校验数据部分，当前流程
                pay_detail = page2.locator('//*[@id="mainDiv"]/div[53]/div[2]').inner_text()
                supplier_name = page2.locator(
                    '//*[@id="PickingInfoDiv"]/div/table/tbody/tr[2]/td[2]').inner_text()
                print("=====================================================")
                print(pay_detail, supplier_name)
                # 获取当初流程
                page2.get_by_role("link", name="挂账/预收/预付记录").click()
                with page2.expect_popup() as page3_info:
                    page2.locator("iframe[name^=\"layui-layer-iframe\"]").content_frame.locator(
                        '//*[@id="mssOpenItemPickingAlreadyPickInfo"]/div/table/tbody/tr[2]/td[19]/a').click()
                page3 = page3_info.value
                old_pay_detail = page3.get_by_title(
                    "说明（尽量详细，至少应包括事由、执行进度、付款批次、发票情况说明等）").inner_text()
                old_supplier_name = page3.locator(
                    '//*[@id="supplierInfoTable"]/tbody/tr[2]/td[2]').inner_text()
                print("-----------------------------------------------------")
                print(old_pay_detail, old_supplier_name)
                page3.close()

                status = 0
                if supplier_name == old_supplier_name:
                    common_subsequence = find_longest_common_subsequence(pay_detail, old_pay_detail)
                    print("最长公共子序列:", common_subsequence)
                    if ('项目' in common_subsequence or '平台' in common_subsequence
                            or '系统' in common_subsequence or '研发' in common_subsequence):
                        status = 1

                if status > 0:
                    print("验证通过,准备提交")
                    # page2.get_by_role("link", name="提交").click()
                    # # 提交按钮,正式改成确定
                    # page2.get_by_text("取消").click()
                page2.close()

            # 判断是否有下一页
            try:
                flag = page1.locator("iframe[name=\"rightside\"]").content_frame.get_by_role("link",
                                                                                             name="下一页").first()
                is_disabled = flag.evaluate('(element) => element.disabled')
                print(is_disabled)
                if is_disabled:
                    break
                else:
                    flag.click()
            except:
                break
        time.sleep(2)
        try:
            context.close()
            browser.close()
        except:
            pass


def get_contract_info(page, selector):
    """
    页面跳转
    """
    with page.expect_popup(timeout=50000) as new_page:
        el = page.query_selector(selector)
        el.click()
    page2 = new_page.value
    page2.wait_for_load_state()
    time.sleep(5)
    return page2


def login_by_account(page, login, account, password):
    """
    账号密码登录
    """
    page.goto(login)
    page.wait_for_load_state()
    page.fill("#freename", account)
    page.fill("#freepassword", password)
    # 执行登录
    page.click('#login_btnDiv > input')
    page.wait_for_load_state()
    return page


def load_account_info(file_path):
    sheet_name = 'Sheet1'
    # 读取人员账号信息Excel文件
    df = pd.read_excel(file_path, sheet_name=sheet_name)  # sheet_name=0表示第一个工作表，也可以使用工作表名称
    data = []
    for idx, r in df.iterrows():
        data.append(r)
    return data


def eip_login(page, eip_username, eip_password):
    eip_login_url = 'http://eip.zjtelecom.cn/pluto/portal/eipPortal.jhtml'
    try:
        page.goto(eip_login_url)
        if page.title() not in 'EIP SSO':
            page.evaluate('location.reload();')
        # 账号登录
        page.fill('[id="inputUserName"]', eip_username)
        print('输入账号')
        page.fill('[id="inputPwd"]', eip_password)
        print('输入密码')
        # 短信登录
        # page.fill('[id="inputPhoneNum"]', eip_username)
        # page.click('//div[@id="verifyCodeBox"]/label[text()="获取验证码"]')
        # 等待输入手机验证码
        # eip_verifycode = input("请输入手机验证码")
        # page.fill('[id="inputVerifyCode"]', eip_username)

        # 点击登录，因为要加载新页面，所以要用到页面导航方法
        with page.expect_navigation():
            page.click('[value="登 录"]')
            print('点击登录')
        time.sleep(random.randint(9, 10))

        for h in range(30):
            hello_el = page.query_selector('//p[contains(text(), "欢迎你:")]')
            if hello_el:
                print('登录成功')
                break
            time.sleep(1)
        time.sleep(2)
        print(page.context.cookies())
    except (Exception, TimeoutError) as e:
        print(f'login err:{e}')
        raise e


if __name__ == '__main__':
    # 本地:获取当前文件夹 所在的本机绝对路径  平台:文件路径、浏览器路径、应用工作流唯一ID、服务端地址
    ley_list = sys.argv
    with sync_playwright() as playwright:
        if len(ley_list) > 1:
            path = ley_list[1]  # path:浏览器前缀统一路径
            id = ley_list[2]  # Id:应用工作流唯一ID
            url = ley_list[3]  # url:服务端地址
            run(playwright, path, id, url)
        else:
            # 本地执行
            run(playwright)
