import re
from playwright.sync_api import Playwright, sync_playwright, expect


def run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context()
    page = context.new_page()
    page.goto("http://sso.zjtelecom.cn/cas/login")
    page.get_by_text("输入用户名").click()
    page.get_by_label("输入用户名").fill("zengsy.xc")
    page.get_by_text("输入密码").click()
    page.get_by_label("输入密码").fill("Zsy131426?")
    page.get_by_role("button", name="登 录").click()
    with page.expect_popup() as page1_info:
        page.locator("li").filter(has_text="集团财辅(3)").get_by_role("img").click()
    page1 = page1_info.value
    page1.get_by_role("link", name="报账平台").click()
    page1.locator("iframe[name=\"rightside\"]").content_frame.get_by_role("link", name="高级查询").click()
    page1.locator("iframe[name=\"rightside\"]").content_frame.locator("#economicItemCode").click()
    page1.locator("iframe[name=\"rightside\"]").content_frame.locator("#economicItemCode").click()
    page1.locator("iframe[name=\"rightside\"]").content_frame.locator("#economicItemCode").fill("02-10070210")
    page1.locator("iframe[name=\"rightside\"]").content_frame.get_by_role("heading", name="高级搜索 确定").locator("span").click()
    page1.locator("iframe[name=\"rightside\"]").content_frame.get_by_role("link", name="高级查询").click()
    page1.locator("iframe[name=\"rightside\"]").content_frame.get_by_role("row", name="是否被共享:    查询").get_by_role("link").click()
    with page1.expect_popup() as page2_info:
        page1.locator("iframe[name=\"rightside\"]").content_frame.get_by_role("link", name="履约计划列账及付款颜瑜琼(A+)信产信产.社会治理事业部").click()
    page2 = page2_info.value
    page2.locator("iframe[name=\"AppFrm\"]").content_frame.get_by_role("cell", name="3,623,753.47").click()
    page2.close()
    page1.close()
    page.close()

    # ---------------------
    context.close()
    browser.close()


with sync_playwright() as playwright:
    run(playwright)
