import re
from playwright.sync_api import Playwright, sync_playwright, expect


def run(playwright: Playwright) -> None:
    browser = playwright.chromium.launch(headless=False)
    context = browser.new_context()
    page = context.new_page()
    page.goto("http://sso.zjtelecom.cn/cas/login")
    page.get_by_text("输入用户名").click()
    page.get_by_label("输入用户名").fill("wanghz.xc")
    page.get_by_label("输入密码").click()
    page.get_by_label("输入密码").fill("xc*13414")
    page.get_by_role("button", name="登 录").click()
    with page.expect_popup() as page1_info:
        page.get_by_text("营收资金稽核(新)").click()
    page1 = page1_info.value
    page1.get_by_role("button", name="高级").click()
    page1.get_by_role("link", name="继续前往134.96.249.29（不安全）").click()
    page1.get_by_role("menuitem", name="营业稽核管理 ").locator("i").click()
    page1.locator("div").filter(has_text=re.compile(r"^营业稽核查询$")).click()
    page1.get_by_text("业务资金管理").click()
    page1.get_by_text("业务资金查询").click()
    page1.get_by_text("报账管理").click()
    page1.get_by_role("link", name="营收结转报账").click()
    page1.locator("div").filter(has_text=re.compile(r"^查询日期$")).get_by_placeholder("选择日期").click()
    page1.get_by_role("button", name="天前").click()
    page1.locator("div").filter(has_text=re.compile(r"^~$")).get_by_placeholder("选择日期").click()
    page1.get_by_role("button", name="今天").click()
    page1.get_by_role("textbox", name="请选择").click()
    page1.get_by_text("营收结转-收入确认的应收挂台账").click()
    page1.get_by_role("button", name=" 搜索").click()
    page1.locator("div").filter(has_text=re.compile(r"^查询日期$")).get_by_placeholder("选择日期").click()
    page1.get_by_role("button", name="昨天").click()
    page1.locator("div").filter(has_text=re.compile(r"^查询日期$")).get_by_placeholder("选择日期").click()
    page1.get_by_role("button", name="一周前").click()
    page1.locator("div").filter(has_text=re.compile(r"^查询日期$")).get_by_placeholder("选择日期").click()
    page1.get_by_role("textbox", name="请选择").click()
    page1.get_by_text("营收结转-开票不确认收入的应收挂台账").click()
    page1.get_by_role("cell", name="审批单号").locator("div").click()
    page1.locator("div").filter(has_text=re.compile(r"^暂无数据$")).nth(1).click()

    # ---------------------
    context.close()
    browser.close()


with sync_playwright() as playwright:
    run(playwright)
